import { computed, ref } from 'vue';
import MyScroller from './scrollerBar';
// 根据浏览器大小推断缩放比例
export const getScale = (width = 1920) => {
  // const ww = window.innerWidth / width;
  // const wh = window.innerHeight / height;
  // return ww < wh ? ww : wh;
  const ww = window.innerWidth / width;
  return ww;
  // return ww;
};

// 监听屏幕缩放自动缩放元素
export const useScreenResize = () => {
  const rate = ref();
  const transform = computed(() => {
    return `scale(${rate.value})`;
  });

  rate.value = getScale();

  const listenerResize = () => {
    window.addEventListener('resize', () => {
      rate.value = getScale();
      MyScroller.refresh();
    });
  };

  const useScroll = (el) => {
    MyScroller.init(el);
  };

  return {
    rate,
    transform,
    listenerResize,
    useScroll,
  };
};

export const getPx = (str) => {
  return str.endsWith('px') ? str : str + 'px';
};
