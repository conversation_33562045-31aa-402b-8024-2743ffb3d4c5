<template>
  <div class="home_container scroll-container">
    <!-- <div class="scale-container" :style="{
        transform,
      }"> -->
    <div class="content">
      <!-- <el-button class="botton" @click="go">返回</el-button> -->
      <!-- <div>{{ JSON.parse(route.query.data) }}</div> -->
      <DataPreviewer height="100%" :url="route.query.url" :fileName="route.query.fileName"></DataPreviewer>
    </div>
    <!-- </div> -->
  </div>

</template>

<script setup>
import { onMounted, ref } from "vue"
import DataPreviewer from "../components/DataPreviewer.vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
import { useScreenResize } from "~/hooks/cockpit/screen_resize";
const { transform, listenerResize, useScroll } = useScreenResize();

// const props = defineProps({
//   data: {
//     type: Object,
//     default: () => {},
//   },
// }) 
const go = () => {
  router.back()
}
const data = ref({})
onMounted(() => {
  // listenerResize();
  // useScroll(document.querySelector(".scroll-container"));
  // console.log('🎁-----route.query.data-----', JSON.parse(route.query.data));
})
</script>

<style lang="less" scoped>
.content {
  width: 100%;
  // height: 500px;
  height: 100vh;
  // padding-left: 20px;
  // padding-right: 20px;
  box-sizing: border-box;
  // transform: translate(-50%, -50%);
}
.botton {
  position: absolute;
  left: 20px;
  top: 20px;
}
</style>