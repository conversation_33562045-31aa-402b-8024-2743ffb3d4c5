<script setup>
import { useUserStore } from "~/store/user";
import { Search, ArrowDown } from "@element-plus/icons-vue";
// import gif from "../assets/user2.png";
import avatar from "../assets/avatar.png";
import nogood from "../assets/good.png";
import good2 from "../assets/good2.png";
import good from "../assets/nogood.png";
import nogood2 from "../assets/nogood2.png";
import headImg from "../assets/AIhead.png";
import err from "../assets/err.png";
import AIimg from "../assets/icon.png";
import animeAiloading from "../assets/anime-ailoading.gif";
import chatLoading from "../assets/chatLoading.gif";
import Popup from "../components/Popup.vue";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { encrypt } from "~/utils/auth/sign";
import { marked } from "marked";
import { useWebSocket } from "@vueuse/core";
import { isString, customDestr, downloadFile } from "@iceywu/utils";
import Cookies from "js-cookie";
// import md5 from "js-md5";
import katex from "katex";
import {
  findAll,
  clientToken,
  askAll,
  questionEvaluate,
  asyncTask,
  questionFindById,
  findFile,
  questfindFileV2,
  checkJson,
  clientlogin,
} from "../api/index";
import { setToken, getToken, formatToken } from "../utils/auth.js";
import { baseUrladmin, baseUrl } from "../utils/http/domain";
import { getLocalStorage, setLocalStorage } from "../utils/index.js";
import { ref } from "vue";
import markdownit from "markdown-it";
import { decrypt } from "../utils/SM4";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const { copy } = useClipboard();
const Tabname = ref("ITINERARY");
const sharedTextareavalue = ref(""); // 为所有input类型的tab共享一个输入框
const platformInfo = JSON.parse(localStorage.getItem("platformInfo") || "{}");
const platformIcon =
  platformInfo.platformIcon ||
  "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==";
console.log(platformIcon);

// 获取URL参数
const urlSig = ref(route.query.sig);
const type = ref(route.query.type);

// 解密sig参数获取userId
const userId = ref("");
if (urlSig.value) {
  try {
    const decryptedData = decrypt(urlSig.value);
    userId.value = decryptedData;
  } catch (error) {}
}

// 创建type参数与本地Tablist的映射关系
const typeMapping = {
  // 从另一个项目的name到当前项目中tab索引的映射
  trip: 0, // 课期行程
  introduce: 1, // 课期介绍
  knowledge: 2, // 课期知识点
  task: 3, // 作业设计
  equipment: 4, // 装备说明
  matter: 5, // 注意事项
  agreement: 6, // 用户协议
  price: 7, // 价格设置
  // foundation和complete不需要处理
};

// 反向映射，从索引到type
const reverseTypeMapping = {
  0: "trip",
  1: "introduce",
  2: "knowledge",
  3: "task",
  4: "equipment",
  5: "matter",
  6: "agreement",
  7: "price",
};

const Username = ref(Date.now());
const easyToken = ref();
async function getclientlogin() {
  // 注释掉原来的登录逻辑
  /*
  // console.log('🌈-----getLocalStorage(?.username-----', getLocalStorage('user')?.username);
  // console.log('🌈-----Cookies.get("authorized-token11")-----', Cookies.get("authorized-token11"));
  if (Cookies.get("authorized-token11") && getLocalStorage("user")?.username) {
    getData();
    // return
  } else {
    if (getLocalStorage("user")?.username) {
      console.log("token失效，重新登录");
      Username.value = getLocalStorage("user").username;
    } else {
      Username.value = Date.now().toString();
    }
    const params = {
      username: Username.value.toString(),
    };
    console.log("🌈-----params-----", params);
    const { code, data = [], msg } = await clientlogin(params);
    if (code == 200 && data) {
      console.log("🦄-----data-----", data);
      accessToken.value = data.token.accessToken;
      // easyToken.value = data.token.accessToken.split('.')[1]
      userId.value = data.user.id;
      // easyToken.value=data.token.accessToken
      // console.log('🍭-----userId.value-----', userId.value);
      setToken(data.token);
      const Userobj = {
        userID: userId.value,
        username: Username.value,
      };
      setLocalStorage("user", Userobj);
      getData();
    } else {
      console.log("🌳-----msg-----", msg);
    }
  }
  */

  // 直接调用getData，不依赖登录状态
  getData();
}

function setActive(item, index) {
  // 注释掉原来的清空逻辑
  // if (item.textareavalue) {
  //   item.textareavalue = "";
  // }
  Tabname.value = item.emun;
  // console.log('🍧-----Tabname.value-----', Tabname.value);
  Tablist.value.forEach((item) => {
    item.state = false;
  });
  Tabtepm.value = Tablist.value[index];
  Tablist.value[index].state = true;

  // 更新URL中的type参数，保持URL与当前选中的Tab同步
  const newType = reverseTypeMapping[index];
  if (newType && newType !== type.value) {
    type.value = newType; // 更新响应式变量

    // 构建新的查询参数对象，保留现有参数
    const query = { ...route.query, type: newType };

    // 使用router.replace更新URL，不增加历史记录
    router.replace({
      path: route.path,
      query,
    });
  }
}
const Tablist = ref([
  {
    tab: 1,
    name: "课期行程",
    emun: "ITINERARY",
    state: true,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 2,
    name: "课期介绍",
    emun: "COURSE_INTRODUCTION",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 3,
    name: "课期知识点",
    emun: "COURSE_KNOWLEDGE_POINT",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 4,
    name: "作业设计",
    emun: "HOMEWORK_DESIGN",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 5,
    name: "装备说明",
    emun: "EQUIPMENT_DESCRIPTION",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 6,
    name: "注意事项",
    emun: "ATTENTION",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  {
    tab: 7,
    name: "用户协议",
    emun: "USER_AGREEMENT",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  // {
  //   tab: 8,
  //   name: "价格设置",
  //   emun: "COST_EXPLANATION",
  //   state: false,
  //   textareavalue: "",
  //   TabTitle: "请描述一下",
  //   placeholder: "请输入您的课程内容",
  //   Maxlength: 300,
  //   type: "input",
  // },
  {
    tab: 9,
    name: "退款政策",
    emun: "REFUND_POLICY",
    state: false,
    textareavalue: "",
    TabTitle: "请描述一下",
    placeholder: "请输入您的课程内容，例如：基于 [研学地点/主题，例如：中山板芙里村/爱国主义教育/里溪三脉] ，设计一个面向 [学段，例如：小学五年级/初中生] 的研学课程。课程核心目标需包含：1、知识目标（3点）、2、能力目标（3点，如探究/合作/表达）、3、情感态度目标（2点）。课程时长 [X] 分钟。",
    Maxlength: 300,
    type: "input",
  },
  // {
  //   tab: 2,
  //   name: "课件",
  //   emun: "COURSEWARE",
  //   state: false,
  //   textareavalue: "",
  //   TabTitle: "请描述一下",
  //   placeholder: "请输入您的需求，如：做一份关于有理数的课件",
  //   Maxlength: 60,
  //   type: "input",
  //   // list:['知识引入','知识讲解','例题','知识点生活应用','总结']
  //   list: [
  //     {
  //       list_name: "知识引入",
  //       value: true,
  //       isDisabled: false,
  //     },
  //     {
  //       list_name: "知识讲解",
  //       value: true,
  //       isDisabled: false,
  //     },
  //     {
  //       list_name: "例题",
  //       value: true,
  //       isDisabled: false,
  //     },
  //     {
  //       list_name: "知识点生活应用",
  //       value: true,
  //       isDisabled: false,
  //     },
  //     {
  //       list_name: "总结",
  //       value: true,
  //       isDisabled: false,
  //     },
  //     // {
  //     //   list_name: "实验",
  //     //   value: false,
  //     // },
  //   ],
  // },
  // {
  //   tab: 3,
  //   name: "教案",
  //   textareavalue: "",
  //   emun: "TEACHING_PLAN",

  //   state: false,

  //   TabTitle: "请描述一下",
  //   placeholder: "请输入您需求，如：做一份关于10位数乘除法的教案",
  //   Maxlength: 60,
  //   type: "input",
  //   list: [
  //     {
  //       list_name: "课前材料",
  //       value: true,
  //     },
  //     {
  //       list_name: "教学目标",
  //       value: true,
  //     },
  //     {
  //       list_name: "课堂实施环节建议",
  //       value: true,
  //     },
  //     {
  //       list_name: "作业",
  //       value: true,
  //     },
  //   ],
  // },
  // {
  //   tab: 4,
  //   name: "讲义",
  //   state: false,

  //   textareavalue: "",
  //   emun: "HANDOUTS",
  //   TabTitle: "请描述一下",
  //   placeholder: "请输入您的需求，如：做一份关于认识小数的讲义",
  //   Maxlength: 60,
  //   type: "input",
  //   list: [
  //     {
  //       list_name: "学习目标",
  //       value: true,
  //     },
  //     {
  //       list_name: "知识点内容梳理",
  //       value: true,
  //     },
  //     {
  //       list_name: "例题",
  //       value: true,
  //     },
  //     {
  //       list_name: "练习题",
  //       value: true,
  //     },
  //     {
  //       list_name: "实践应用",
  //       value: true,
  //     },
  //   ],
  // },
  // {
  //   tab: 5,
  //   name: "帮我写作",
  //   textareavalue: "",
  //   emun: "TALK",
  //   state: false,
  //   TabTitle: "请描述一下",
  //   type: "input",
  //   placeholder: "请输入您的需求，如：做一份关于热爱祖国主题的班会大纲文案",
  //   Maxlength: 300,
  // },
  // {
  //   tab: 6,
  //   name: '出试卷',
  //   state: false,
  //   Rate: 'true',
  //   emun: 'EXAMINATION',
  //   Tepmlable: {
  //     selectvalue: '', //下拉
  //     searchvalue: '', //搜索
  //     Ratevalue: 0, //评分
  //   },
  //   option: [
  //     {
  //       label: "全部",
  //       value: "false"
  //     },
  //     {
  //       label: "全部2",
  //       value: "false2"
  //     }
  //   ],
  //   type: 'select',
  //   placeholder: '搜索知识点',
  // }
]);

const Tabtepm = ref(Tablist.value[0]);
const resetLoading = ref(false);
const centerDialogVisible = ref(false); //打开弹窗
const setPopu = ref(""); //弹窗样式
const context = ref();
const oldText = ref();
const isd = ref(true);
const isDeepThinking = ref(false); // 添加深度思考状态变量
// const Twotabloading = ref(false)
// 提交
const submit = async (val) => {
  // console.log('🎉-----val-----', val);
  // Twotabloading.value = true
  openloading.value = true;
  switchText.value = false;
  // 第二次一键生成对话
  if (val) {
    console.log(val);
    // masgArr.value.splice(index, 1);
    let newVal = {
      createdAt: "",
      updatedAt: "",
      description: "请帮我生成对应的PPT课件",
      askType: "COURSEWARE",
      thinkingContent: "", // 初始化深度思考内容
      thinkingStatus: "thinking", // 初始化思考状态
      answerDTOS: [
        {
          id: "",
          experimentReply: "",
          createdAt: "",
          updatedAt: "",
          liked: "",
          disLiked: "",
          reply: "",
          courseQuestionId: null,
        },
      ],
    };
    masgArr.value.push(newVal);
    console.log("🐬生成课件后-masgArr.value-----", masgArr.value);
    const oldHeight = chatContainer.value.scrollHeight; // 记录旧的滚动高度
    await nextTick();
    const newHeight = chatContainer.value.scrollHeight; // 计算新的滚动高度
    chatContainer.value.scrollTop += newHeight - oldHeight; // 保持当前的滚动位置
    scrollToBottom();
    chatAi2(val, accessToken.value, !isd);
    return;
  }
  // 针对 'input' 类型进行检查
  if (Tabtepm.value.type === "input" && !sharedTextareavalue.value) {
    ElMessage({
      type: "warning",
      message: "请输入您想描述的内容",
      customClass: "element-bl-pc-sc",
    });
    return;
  }
  masgArr.value.forEach((item) => {
    if (item.applyEffect) {
      item.applyEffect = false;
    }
  });
  // 针对 'select' 类型进行检查
  if (Tabtepm.value.type === "select") {
    const { selectvalue, searchvalue, Ratevalue } =
      Tabtepm.value.Tepmlable || {};
    console.log(
      "🍧-----selectvalue, searchvalue, Ratevalue-----",
      selectvalue,
      Ratevalue
    );
    if (!selectvalue && !searchvalue && Ratevalue <= 0) {
      ElMessage({
        message: "请选择或填写相应的内容",
        type: "warning",
      });
      return;
    }
  }

  if (resetLoading.value) return;
  resetLoading.value = true;
  console.log("🐳-----Tabtepm-----", Tabtepm.value);

  let params = {};
  console.log("🎉-params.askType----Tabname.value-----", Tabname.value);
  if (Tabtepm.value) {
    params.askType = Tabtepm.value.emun;
  }
  params.contentType = [];

  // 添加深度思考模式参数
  if (isDeepThinking.value) {
    params.deepThinking = true;
  }

  if (Tabtepm.value.name) {
    params.contentType.push("实验");
    let newVal = {
      createdAt: "",
      updatedAt: "",
      applyEffect: true,
      description: sharedTextareavalue.value,
      askType: "TALK",
      thinkingContent: "", // 初始化深度思考内容
      thinkingStatus: "thinking", // 初始化思考状态
      answerDTOS: [
        {
          id: "",
          experimentReply: "",
          createdAt: "",
          updatedAt: "",
          liked: "",
          disLiked: "",
          reply: "",
          courseQuestionId: null,
        },
      ],
    };
    if (Tabtepm.value.name === "行程安排") {
      isd.value = false;
      newVal.askType = "ITINERARY";
    }
    if (Tabtepm.value.name === "课件") {
      isd.value = false;
      newVal.askType = "COURSEWARE";
    }
    // console.log("🍪-----newVal----444-", newVal);
    // return
    masgArr.value.push(newVal);
    params.description = sharedTextareavalue.value;
    // chatAi(params, accessToken.value);
    console.log("🐬---结果--params-----", params);
    const oldHeight = chatContainer.value.scrollHeight; // 记录旧的滚动高度
    await nextTick();
    const newHeight = chatContainer.value.scrollHeight; // 计算新的滚动高度
    chatContainer.value.scrollTop += newHeight - oldHeight; // 保持当前的滚动位置
    scrollToBottom();

    chatAi2(params, accessToken.value, isd);
    return;
  }
  // 判断type类型
  if (Tabtepm.value.type == "input") {
    if (sharedTextareavalue.value) {
      params.description = sharedTextareavalue.value;
    }
    if (Tabtepm.value.list) {
      // params.contentType = [];
      Tabtepm.value.list.forEach((item) => {
        if (item.value) {
          params.contentType.push(item.list_name);
        }
        // console.log('🍭-----item-----', item.value);
      });
    }
  } else if (Tabtepm.value.type == "select") {
    for (let key in Tabtepm.value.Tepmlable) {
      if (Tabtepm.value.Tepmlable.hasOwnProperty(key)) {
        if (Tabtepm.value.Tepmlable[key]) {
          params[key] = Tabtepm.value.Tepmlable[key];
        }
      }
    }
  }
  let newVal = {
    createdAt: "",
    updatedAt: "",
    applyEffect: true,
    description: sharedTextareavalue.value,
    askType: "TALK",
    thinkingContent: "", // 初始化深度思考内容
    thinkingStatus: "thinking", // 初始化思考状态
    answerDTOS: [
      {
        id: "",
        experimentReply: "",
        createdAt: "",
        updatedAt: "",
        liked: "",
        disLiked: "",
        reply: "",
        courseQuestionId: null,
      },
    ],
  };
  masgArr.value.push(newVal);
  const oldHeight = chatContainer.value.scrollHeight; // 记录旧的滚动高度
  await nextTick();
  const newHeight = chatContainer.value.scrollHeight; // 计算新的滚动高度
  chatContainer.value.scrollTop += newHeight - oldHeight; // 保持当前的滚动位置
  scrollToBottom();
  // return
  const res = await askAll(params);
  // console.log("🌈-----接口请求成功-----", res);
  if (res) {
    const { code, msg, data = [] } = res || {};
    if (code === 200) {
      startTextSwitch(); // 开始文本切换
      getasyncTask(data.id);
      console.log("😊-----数据获取成功-----", data);
    } else {
      resetLoading.value = false;
      console.log("😒-----数据获取失败-----", msg);
    }
  }
};

// 提交2
const submit2 = async (val, index, Err) => {
  console.log("🎁-----val-----", val);
  console.log("🍪-----Err-----", Err);
  let params = {
    description: val,
    askType: Tabname.value,
    isDeepSeek: isDeepThinking.value, // 添加深度思考参数，默认为false
  };

  console.log("🎁-----params-----", params);

  // return
  masgArr.value.splice(index, 1);
  let newVal = {
    createdAt: "",
    updatedAt: "",
    description: val,
    askType: "TALK",
    thinkingContent: "", // 初始化深度思考内容
    thinkingStatus: "thinking", // 初始化思考状态
    answerDTOS: [
      {
        id: "",
        experimentReply: "",
        createdAt: "",
        updatedAt: "",
        liked: "",
        disLiked: "",
        reply: "",
        courseQuestionId: null,
      },
    ],
  };
  masgArr.value.push(newVal);
  if (Err) {
    chatAi2(params, accessToken.value);
    return;
  }
  const oldHeight = chatContainer.value.scrollHeight; // 记录旧的滚动高度
  await nextTick();
  const newHeight = chatContainer.value.scrollHeight; // 计算新的滚动高度
  chatContainer.value.scrollTop += newHeight - oldHeight; // 保持当前的滚动位置
  scrollToBottom();

  // return
  console.log("🐳-----params-----", params);
  const res = await askAll(params);
  // console.log("🌈-----接口请求成功-----", res);
  if (res) {
    const { code, msg, data = [] } = res || {};
    if (code === 200) {
      getasyncTask(data.id);
      // console.log("😊-----数据获取成功-----", data);
    } else {
      resetLoading.value = false;
      console.log("😒-----数据获取失败-----", msg);
    }
  }
};

const createdAtTime = ref();
// const errMsg = ref(false)
const updatedAtTime = ref(false); //弹窗状态
const condition = ref(false); //查询状态
let timer;
const getasyncTask = async (id) => {
  resetLoading.value = true;

  await asyncTask({ id }).then(async (s) => {
    const { code, msg, data = [] } = s || {};
    // console.log(s, "异步查询");
    if (data.errMsg) {
      console.log("查询失败查询失败查询失败1");
      condition.value = false; //查询失败
      resetLoading.value = false;
      // 保留用户输入，不清空输入框
      // Tabtepm.value.textareavalue = "";
      masgArr.value[masgArr.value.length - 1].tip = true;
      masgArr.value[masgArr.value.length - 1].errMsg = true;
      clearTimeout(timer); //清理定时任务
      clearTextSwitch(); // 清理文本切换任务
      await nextTick();
      scrollToBottom(); // 页面加载后自动滚动到底部
      return;
    }
    if (data.complete == false && data.success) {
      timer = setTimeout(() => {
        getasyncTask(id);
      }, 1000); //1秒查一下
    } else {
      resetLoading.value = false;
      clearTimeout(timer); //清理定时任务
      clearTextSwitch(); // 清理文本切换任务
      switchText.value = false; // 显示文本
      getNewData(data.resData);
    }
  });
  // console.log('res2: ', res2.result.complete);
};
const getNewData = async (id) => {
  const paramsData = {
    questionId: Number(id),
  };
  const res = await questionFindById(paramsData);
  // console.log("🌳-----根据问题Id查询详情-----", res);
  // return;
  if (res) {
    const { code, msg, data = [] } = res || {};
    if (code === 200) {
      // masgArr.value.push(data);
      data.applyEffect = true;
      // console.log('🍪-----masgArr.value[masgArr.value.length - 1]-----', masgArr.value[masgArr.value.length - 1]);
      masgArr.value[masgArr.value.length - 1] = data;
      openloading.value = false;
      // console.log('🐠-----masgArr.value[masgArr.value.length - 1] -----', masgArr.value[masgArr.value.length - 1] );
      // console.log('🐳-----data-----', data);
      // updatedAtTime.value = false;
      // condition.value = true;
      // centerDialogVisible.value = false;
      // setTimeout(() => {
      //   centerDialogVisible.value = false;
      // }, 1000)
      await nextTick();
      scrollToBottom(); // 页面加载后自动滚动到底部
      // getData()
    } else {
      updatedAtTime.value = false;
      condition.value = false;
      centerDialogVisible.value = false;
      openloading.value = false;
      console.log("😒-----根据问题Id查询详情失败-----", msg);
    }
  }
  // Tabtepm.value.textareavalue = "";
  if (Tabtepm.value.name === "出试卷") {
    Tabtepm.value.Tepmlable.selectvalue = "";
    Tabtepm.value.Tepmlable.searchvalue = "";
    Tabtepm.value.Tepmlable.Ratevalue = 0;
  }
  resetLoading.value = false;
};

const setCenterDialogVisible = () => {
  centerDialogVisible.value = false;
  console.log(
    "🌵-----centerDialogVisible.value -----",
    centerDialogVisible.value
  );
};

const newche = ref();
const newcheckJson = async (id, askType) => {
  const params = {
    questionId: id,
    askType: askType,
  };
  console.log("🌳---777777777777777777777--params-----", params);
  const res = await checkJson(params);
  console.log("🌵-----res-----", res);
  if (res) {
    console.log("🌈-----接口请求成功-----");
    const { code, msg, data = [] } = res || {};
    if (code === 200 && data) {
      newche.value = data.id;
      console.log("😊-----数据获取成功-----", data.id);

      return data.complete;
    } else {
      console.log("😒-----数据获取失败-----", msg);
    }
  }
};

const unindex1 = ref({});
const unindex2 = ref({});
const unindex3 = ref();
const unindex4 = ref();
const masgArr = ref([]);
const dialogVisible2 = ref(false);
const open2 = async (val, val2, index, index2) => {
  console.log("🌵-----val-----", val);
  console.log("🐠-----val2-----", val2);
  console.log("🦄-----index-----", index);
  console.log("🍪-----index2-----", index2);
  // return
  condition.value = false;
  let url = ""; //要打开的 URL
  let previewUrl = ""; // 文件预览地址
  let urlName = "";
  let obj = {};
  const getquestfindFileV = async (id) => {
    console.log("🐠-----id>>>>>>>>>>>>>>>>>-----", id);
    resetLoading.value = true;
    await asyncTask({ id }).then(async (s) => {
      const { code, msg, data = [] } = s || {};
      console.log(s, "异步查询");
      if (data.errMsg) {
        resetLoading.value = false;
        // Tabtepm.value.textareavalue = "";
        setTimeout(() => {
          console.log("1212123123123123123");
          setPopu.value = val.askType; //提问类型
          updatedAtTime.value = false;
          setTimeout(() => {
            console.log("89898989898989898989898989898989");
            condition.value = false;
            centerDialogVisible.value = false;
          }, 3000);
        }, 3000);
        // if (condition.value) {
        // masgArr.value[masgArr.value.length - 1].tip = true;
        clearTimeout(timer); //清理定时任务
        clearTextSwitch(); // 清理文本切换任务
        // await nextTick();
        // scrollToBottom(); // 页面加载后自动滚动到底部
        return;
      }
      if (data.complete == false && data.success) {
        timer = setTimeout(() => {
          getquestfindFileV(id);
        }, 1000); //1秒查一下
      } else {
        resetLoading.value = false;
        condition.value = true; //查询成功
        clearTimeout(timer); //清理定时任务
        clearTextSwitch(); // 清理文本切换任务
        // switchText.value = false;  // 显示文本
        // getNewData(data.resData);
        // console.log('🦄-----data-----', data);
        previewUrl = data.fileUrl;
        urlName = data.fileName;
        // url = `${baseUrladmin.apiServer}#/previewer?data={"url":"${previewUrl}","fileName":"${urlName}"}`; // 要打开的
        url = `${baseUrladmin.apiServer}#/previewer?url=${previewUrl}&fileName=${urlName}`;
        setTimeout(() => {
          updatedAtTime.value = false;
          // centerDialogVisible.value = false; //关闭弹窗

          setTimeout(() => {
            centerDialogVisible.value = false;
            if (condition.value) {
              window.open(url);
              masgArr.value[index].answerDTOS[index2].url = previewUrl;
              masgArr.value[index].answerDTOS[index2].fileName = urlName;
            }
          }, 1000);
        }, 3000);
      }
    });
  };
  console.log("🍪--//---val2.experimentReply-----", val2);
  if (val2.url) {
    obj.url = val2.url;
    obj.fileName = val2.fileName;
    // url = `${baseUrladmin.apiServer}#/previewer?data=${JSON.stringify(obj)}`; // 要打开的 URL
    url = `${baseUrladmin.apiServer}#/previewer?url=${obj.url}&fileName=${obj.fileName}`;
    console.log("🦄-----url-----", url);
    // return
    window.open(url);
  } else if (val2.experimentReply) {
    const completeValue = await newcheckJson(val.id, val.askType);
    console.log("🌳-----completeValue-----", completeValue);
    let questionId = val.id;
    if (completeValue) {
      centerDialogVisible.value = true; //打开弹窗
      setPopu.value = val.askType; //提问类型
      updatedAtTime.value = true; //查询结束
      // 判断 questionId 是否包含换行符 \n，如果包含则去除
      if (/\n/.test(questionId)) {
        questionId = questionId.replace(/\n/g, "");
      }
      let { code, data } = await questfindFileV2({
        askType: val.askType,
        questionId,
      });
      console.log("🍪-----data-----", data);
      if (code == 200 && data.id) {
        if (data.complete) {
          console.log("🎉-----data.complete-----", data.complete);
          console.log("🍕🍕🍕🍕🍕🍕🍕🍕,进入查看");
          obj.url = data.fileUrl;
          obj.fileName = data.fileName;
          console.log("🎁-----obj>>>>>>>>>>>>-----", obj);
          // url = `${baseUrladmin.apiServer}#/previewer?data=${JSON.stringify(obj)}`; // 要打开的 URL
          url = `${baseUrladmin.apiServer}#/previewer?url=${obj.url}&fileName=${obj.fileName}`;
          // window.open(url);
          condition.value = true; //查询成功
          setTimeout(() => {
            console.log("*********************************", data);
            updatedAtTime.value = false;
            setTimeout(() => {
              centerDialogVisible.value = false;
              if (condition.value) {
                window.open(url);
                masgArr.value[index].answerDTOS[index2].url = obj.url; //改变按钮状态
                masgArr.value[index].answerDTOS[index2].fileName = obj.fileName; //改变按钮状态
              }
            }, 1000);
          }, 3000);
        } else {
          console.log("🌈-----data.id-----", data);
          getquestfindFileV(data.id);
        }
      } else {
        condition.value = false; //查询失败
      }
    } else {
      centerDialogVisible.value = true; //打开弹窗
      setPopu.value = val.askType; //提问类型
      updatedAtTime.value = true; //查询中...
      unindex1.value = val;
      unindex2.value = val2;
      unindex3.value = index;
      unindex4.value = index2;
      console.log("🍭-----unindex-----", val, val2);
      console.log("🍭-----unindex3.value-----", unindex3.value);
      getasyncTask2(newche.value);
      return;
    }
  } else {
    centerDialogVisible.value = true; //打开弹窗
    setPopu.value = val.askType; //提问类型
    updatedAtTime.value = true; //查询结束
    // 接口请求参数
    const params = {
      askType: val.askType,
      answerId: val2.id,
    };

    // 调用接口
    const res = await findFile(params);
    console.log("🌳-----查看ppt-word文件-----", res);

    if (res) {
      const { code, msg, data = [] } = res || {};
      if (code === 200) {
        previewUrl = data.url;
        urlName = data.fileName;
        // url = `${baseUrladmin.apiServer}#/previewer?data=${JSON.stringify(data)}`; // 要打开的 URL
        url = `${baseUrladmin.apiServer}#/previewer?url=${previewUrl}&fileName=${urlName}`;
        condition.value = true; //查询成功
      } else {
        // ElMessage.error('生成失败');
        condition.value = false; //查询失败
        // console.log("😒-----查看ppt-word文件-----", msg);
      }
    }
    // 设置计时器并在计时结束后调用接口
    setTimeout(() => {
      updatedAtTime.value = false;
      setTimeout(() => {
        centerDialogVisible.value = false;
        if (condition.value) {
          window.open(url);
          masgArr.value[index].answerDTOS[index2].url = previewUrl; //改变按钮状态
          masgArr.value[index].answerDTOS[index2].fileName = urlName; //改变按钮状态
        }
      }, 1000);
    }, 3000);
  }
};
const getasyncTask2 = async (id) => {
  resetLoading.value = true;
  await asyncTask({ id }).then(async (s) => {
    const { code, msg, data = [] } = s || {};
    // console.log(s, "异步查询");
    // if (data.errMsg) {
    //   console.log('查询失败查询失败查询失败2');
    //   resetLoading.value = false;
    //   updatedAtTime.value = false;
    //   centerDialogVisible.value = false;//关闭弹窗
    //   Tabtepm.value.textareavalue = "";
    //   masgArr.value[masgArr.value.length - 1].tip = true;
    //   masgArr.value[masgArr.value.length - 1].errMsg = true;
    //   return;
    // }
    if (data.errMsg) {
      resetLoading.value = false;
      console.log("查询失败查询失败查询失败2");
      setTimeout(() => {
        // setPopu.value = 'COURSEWARE'; //提问类型
        updatedAtTime.value = false; //查询失败
        // 保留用户输入，不清空输入框
        // Tabtepm.value.textareavalue = "";
        setTimeout(() => {
          condition.value = false;
          centerDialogVisible.value = false;
          // masgArr.value[masgArr.value.length - 1].tip = true;
          // masgArr.value[masgArr.value.length - 1].errMsg = true;
        }, 3000);
      }, 3000);
      return;
    }

    if (data.complete == false && data.success) {
      timer = setTimeout(() => {
        getasyncTask2(id);
      }, 1000); //1秒查一下
    } else {
      resetLoading.value = false;
      clearTimeout(timer); //清理定时任务
      open2(unindex1.value, unindex2.value, unindex3.value, unindex4.value);
    }
  });
  // console.log('res2: ', res2.result.complete);
};
const dataHandle = (val) => {
  // console.log("🐠-----val-----", val);
  const propertiesToRemove = [
    "id",
    "url",
    "createdAt",
    "updatedAt",
    "liked",
    "disLiked",
    "fileName",
    "reply",
    "experiment",
  ];
  // console.log('转化', Object.entries(val));

  const filteredObject = Object.fromEntries(
    Object.entries(val).filter(([key]) => !propertiesToRemove.includes(key))
  );
  // console.log('🍧-----filteredObject-----', filteredObject);
  let title = {
    introduce: "知识引入",
    explanation: "知识讲解",
    example: "例题",
    application: "知识点生活应用",
    summary: "总结",
    materials: "课前材料",
    teachingAim: "教学目标",
    advice: "课堂实施环节建议",
    homework: "作业",
    learningObjectives: "学习目标",
    knowledgePoint: "知识点内容梳理",
    practice: "练习题",
    practicalApplication: "实践应用",
  };
  // 将剩下的值组成一个字符串，并在每个属性后换行
  const combinedString = Object.entries(filteredObject)
    .map(([key, value]) => {
      if (title[key] && value) {
        // 仅当有匹配标题和内容时才添加到字符串中
        if (Array.isArray(value)) {
          // 将数组的每个项用 <br> 分开
          const formattedArray = value.map((item) => `${item}`).join("\n");
          return `${title[key]}:\n${formattedArray}`;
        }
        return `${title[key]}\n${value}`;
      }
      return value ? `${value}` : ""; // 如果没有标题但有值，返回值；否则返回空字符串
    })
    .filter(Boolean) // 过滤掉空字符串
    .join("\n\n"); // 每对标题和内容之间添加额外换行

  return combinedString;
};

// 复制
const copyText = async (val, item) => {
  let textToCopy = ""; // 用于存储最终要复制的纯文本或Markdown文本

  if (item.askType == "TALK") {
    if (val?.reply) {
      // 对于TALK类型，期望复制原始的Markdown文本
      textToCopy = val.reply;
    } else if (val?.experimentReply) {
      // 实时聊天的 experimentReply 通常也是Markdown
      textToCopy = val.experimentReply;
    }
  } else {
    // 非TALK类型的处理, dataHandle 返回的是拼接好的字符串，可能包含标题等
    // 期望复制这种拼接好的文本，而不是单个字段的纯Markdown
    textToCopy = dataHandle(val);
  }

  // 如果是实时消息，还需要经过 processExperimentReply 处理
  if (val?.experimentReply && item.askType !== "TALK") {
    // 确保不是TALK类型下的experimentReply
    textToCopy = processExperimentReply(textToCopy);
  }

  if (!textToCopy) {
    ElMessage.warning("没有可复制的内容");
    return;
  }

  // 调用 fallbackCopyText，它会处理Markdown到HTML的转换
  fallbackCopyText(textToCopy);
};

const fallbackCopyText = (markdownText) => {
  const md = markdownit();
  // 将Markdown文本转换为HTML
  const htmlResult = md.render(markdownText || "");

  // 创建一个临时的div元素来容纳HTML内容，以便进行复制
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = htmlResult;
  tempDiv.style.position = "fixed";
  tempDiv.style.left = "-9999px"; // 移出屏幕外
  tempDiv.style.top = "0";
  document.body.appendChild(tempDiv);

  let success = false;
  try {
    // 选中文本
    const range = document.createRange();
    range.selectNodeContents(tempDiv);
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    // 执行复制命令
    success = document.execCommand("copy");

    if (success) {
      ElMessage.success("复制成功");
    } else {
      // ElMessage.error("复制富文本失败，已尝试复制纯文本");
      // 富文本复制失败，尝试复制原始Markdown纯文本作为后备
      const textarea = document.createElement("textarea");
      textarea.value = markdownText || "";
      textarea.style.position = "fixed";
      textarea.style.left = "-9999px";
      textarea.style.top = "0";
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();
      try {
        document.execCommand("copy");
        ElMessage.success("复制成功");
      } catch (e) {
        ElMessage.error("复制失败");
      }
      document.body.removeChild(textarea);
    }
  } catch (err) {
    console.error("复制失败", err);
    ElMessage.error("复制失败");
  } finally {
    // 清理DOM
    document.body.removeChild(tempDiv);
    if (window.getSelection) {
      window.getSelection().removeAllRanges();
    }
  }
};

const accessToken = ref();
onMounted(async () => {
  // 如果URL中有type参数并且能找到对应的tab索引，自动选中该tab
  if (type.value && typeMapping[type.value] !== undefined) {
    const tabIndex = typeMapping[type.value];
    const tabItem = Tablist.value[tabIndex];
    setActive(tabItem, tabIndex);
  }

  // 原有的onMounted逻辑
  getData();
});

const getAccessToken = async () => {
  const params = {
    grant_type: "client_credentials",
    // grant_type:'sdkfjhdsifjsdlfjhksdfk',
    client_id: "U9icuroNG2iupfypwxuRar20IAqMPUpS",
    client_secret:
      "0cZXcnS3GRXiPhV44yLaOyEJA1nilvepJ3CImfDDckG4Rh0iet6XhoWv3aMrlTVT",
  };
  // to is a function form (@iceywu/utils)
  const res = await clientToken(params);
  if (res) {
    // console.log("🌈-----接口请求成功-----", res);
    const { code, msg, data = [] } = res || {};
    if (code === 200 && data) {
      accessToken.value = data.accessToken;
      setToken(data);
      getData();
      console.log("😊--LAO---数据获取成功-----", data);
    } else {
      console.log("😒-----数据获取失败-----", msg);
    }
  }
  // if (err) {
  //   console.log('❗-----接口请求失败-----');
  // }
};

const params3 = ref({
  page: 0, // 从0开始
  size: 10,
  totalElements: 0,
  totalPages: 0, // 添加总页数
  last: false,
  currentPage: 0, // 当前已加载的最大页码
  firstQuestionId: 0, // 添加记录第一条数据的 id
});
const listRef = ref();
const scrollTopData = ref();
const lastData = ref();
const questionId = ref(0);
const getDataLoading = ref(false);
const chatContainer = ref(null);
const scrollPosition = ref(0);
// 获取数据的方法
const getData = async (isLoadMore = false) => {
  if (getDataLoading.value) return;
  masgArr.value.forEach((item) => {
    if (item.applyEffect) {
      item.applyEffect = false;
    }
  });
  getDataLoading.value = true;

  // 根据加载模式确定请求的页码
  const pageToLoad = isLoadMore ? params3.value.currentPage + 1 : 0;

  const paramsData = {
    userId: userId.value,
    page: pageToLoad,
    size: params3.value.size,
  };

  // 如果是加载更多且有上一次记录的第一条数据id，则添加到请求参数中
  if (isLoadMore && params3.value.firstQuestionId) {
    paramsData.questionId = params3.value.firstQuestionId;
  }

  console.log("🌳-----请求参数-----", paramsData);
  try {
    const res = await findAll(paramsData);
    console.log("🌳-----API返回数据-----", res);

    if (res && res.code === 200) {
      // 更新分页信息
      params3.value.totalElements = res.data.totalElements;
      params3.value.totalPages = res.data.totalPages;
      params3.value.last = res.data.last;

      if (isLoadMore) {
        params3.value.currentPage = pageToLoad;
      } else {
        params3.value.currentPage = 0;
        // 首次加载时，记录第一条数据的id
        if (res.data.content.length > 0) {
          params3.value.firstQuestionId = res.data.content[0].id;
        }
      }

      // 转换数据结构以匹配旧格式
      const convertedContent = res.data.content.map((item) => {
        return {
          ...item,
          askType: item.askType || "TALK", // 添加默认的askType
          // 将单一的 reply 转换为 answerDTOS 数组格式
          answerDTOS: [
            {
              id: item.id,
              createdAt: item.createdAt,
              updatedAt: item.updatedAt,
              reply: item.reply || "", // 确保reply有值，避免undefined
              liked: false,
              disLiked: false,
              experimentReply: item.experimentReply || "",
              courseQuestionId: null,
            },
          ],
        };
      });

      // 对当前页数据进行倒序处理
      const reversedContent = [...convertedContent].reverse();

      if (isLoadMore) {
        // 加载更多（更早的消息），插入到数组前面
        const oldHeight = chatContainer.value.scrollHeight; // 记录旧的滚动高度
        masgArr.value.unshift(...reversedContent);
        await nextTick();
        const newHeight = chatContainer.value.scrollHeight; // 计算新的滚动高度
        chatContainer.value.scrollTop += newHeight - oldHeight; // 保持当前的滚动位置
      } else {
        // 首次加载，直接设置数组
        if (res.data.content.length > 0) {
          questionId.value = res.data.content[0]?.id || 0; // 使用最新消息的ID
        }
        masgArr.value = reversedContent;
        await nextTick();
        scrollToBottom(); // 首次加载，滚动到底部
      }
    }
  } catch (error) {
    console.error("获取历史数据失败", error);
  }
  getDataLoading.value = false;
};

// 滚动到底部的方法
const scrollToBottom = () => {
  // 添加平滑滚动样式
  chatContainer.value.style.scrollBehavior = "smooth";
  chatContainer.value.scrollTop = chatContainer.value?.scrollHeight;

  // 滚动完成后恢复默认滚动行为
  setTimeout(() => {
    chatContainer.value.style.scrollBehavior = "auto";
  }, 500);
};
const scrollToBottom2 = () => {
  const threshold = 150; // 定义一个阈值
  const distanceToBottom =
    chatContainer.value.scrollHeight -
    chatContainer.value.scrollTop -
    chatContainer.value.clientHeight;

  if (distanceToBottom <= threshold) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};
// 监听滚动事件，触发加载更多
const onScroll = () => {
  if (chatContainer.value?.scrollTop === 0 && !getDataLoading.value) {
    // 检查是否还有更多页面可以加载
    if (
      !params3.value.last &&
      params3.value.currentPage < params3.value.totalPages - 1
    ) {
      getData(true);
      return;
    }
  }

  // 检测是否距离底部有一定距离，如果有则显示回到底部按钮
  const distanceToBottom =
    chatContainer.value.scrollHeight -
    chatContainer.value.scrollTop -
    chatContainer.value.clientHeight;

  // 如果距离底部超过150px，显示回到底部按钮
  showBackBottom.value = distanceToBottom > 150;
};

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

//点赞and取消点赞
const give = (item, item2, index, index2) => {
  console.log("🌈-----item-----", item, index);
  if (item2.liked) {
    masgArr.value[index].answerDTOS[index2].liked = false;
    debouncedLike(item2.id, item.askType, false, "LIKE", item.id);
  } else {
    masgArr.value[index].answerDTOS[index2].liked = true;
    masgArr.value[index].answerDTOS[index2].disLiked = false;
    debouncedLike(item2.id, item.askType, true, "LIKE", item.id);
  }
};
const give2 = (item, item2, index, index2) => {
  console.log("🎉-----item2-----", item2);
  if (item2.disLiked) {
    masgArr.value[index].answerDTOS[index2].disLiked = false;
    debouncedLike2(item2.id, item.askType, false, "DISLIKE", item.id);
  } else {
    masgArr.value[index].answerDTOS[index2].disLiked = true;
    masgArr.value[index].answerDTOS[index2].liked = false;
    debouncedLike2(item2.id, item.askType, true, "DISLIKE", item.id);
  }
};

//点赞
const getDataLoading2 = ref(false);
const like = async (id, item, val, type, id2) => {
  if (getDataLoading2.value) return;
  getDataLoading2.value = true;
  const paramsData = {
    // answerId: id?id:Number(id2.split('\n')[0]),
    // askType: item,
    evaluate: val,
    evaluateType: type,
    // questionId:id || Number(id2.split('\n')[0]),
  };
  if (id) {
    paramsData.answerId = id;
    paramsData.askType = item;
  } else {
    paramsData.questionId = Number(id2);
    paramsData.askType = item;
  }
  const res = await questionEvaluate(paramsData);
  console.log("🌳-----点赞-----", res);
  // return;
  if (res) {
    const { code, msg, data = [] } = res.data || {};
    if (code === 200) {
      if (type == "LIKE") {
        // ElMessage.success(val ? "点赞成功" : "取消点赞");
      } else {
        // ElMessage.success(val ? "点踩成功" : "取消点踩");
      }
      // getData()
    } else {
      console.log("😒-----点赞失败-----", msg);
    }
  }
  getDataLoading2.value = false;
};
const debouncedLike = debounce(like, 300);
const debouncedLike2 = debounce(like, 300);

// 二次回复
const openImg3 = ref(false);
// 二次loading
const openloading = ref(false);
const openindex = ref({
  index1: 0,
  index2: 0,
  openindex: false,
});
const open3 = (val, val2, index, index2) => {
  console.log("🌈-----val-----", val);
  console.log("🐬-----val2-----", val2);
  console.log("🌳-----index-----", index);
  console.log("🦄-----index2-----", index2);
  console.log("*******************", masgArr.value[index].answerDTOS[index2]);
  // return
  openImg3.value = true;
  openloading.value = true;
  const params = {};
  params.lastQuestionId = val.id;
  params.description = "请帮我生成对应的PPT课件";
  params.askType = "COURSEWARE";
  params.courseware = false;
  resetLoading.value = true;
  openindex.value.index1 = index;
  openindex.value.index2 = index2;
  openindex.value.openindex = true;
  submit(params);
};

// 类型判断
const askType = (val, content) => {
  let typeName = {
    ITINERARY: "文档",
    COURSEWARE: "课件",
    TEACHING_PLAN: "教案",
    HANDOUTS: "讲义",
    TALK: "文档",
    EXAMINATION: "试卷",
  };
  // if (val == "COURSEWARE" || val == "TEACHING_PLAN" || val == "HANDOUTS" || val == "TALK") {
  //   if (content) {
  //     return "查看";
  //   } else {
  //     return `一键生成${typeName[val]}`;
  //   }
  // } else {
  //   return false;
  // }
  if (content) {
    if (val == "ITINERARY") {
      return "查看文档";
    }
    return "查看";
  } else if (val == "ITINERARY") {
    return `生成${typeName[val]}`;
  } else {
    return `一键生成${typeName[val]}`;
  }
};

const isInputValid = computed(() => {
  const isTextValid = sharedTextareavalue.value?.trim() !== "";

  const isSelectValid = Tabtepm.value.Tepmlable?.selectvalue !== "";

  const isSearchValid = Tabtepm.value.Tepmlable?.searchvalue?.trim() !== "";

  const isRateValid = Tabtepm.value.Tepmlable?.Ratevalue > 0;

  // 根据 Tab 类型判断输入框或选项是否有值
  if (Tabtepm.value.type === "input") {
    return isTextValid;
  } else if (Tabtepm.value.type === "select") {
    return isSelectValid || isSearchValid || isRateValid;
  }
  return false;
});

const handleComplete = () => {
  scrollToBottom2();
};
const dataHandle2 = (val, val2) => {
  // console.log('🍭-dataHandle2----val-----', val);
  // console.log('🌳-ataHandle2--val2-----', val2);
  const propertiesToRemove = [
    "id",
    "fileName",
    "url",
    "createdAt",
    "updatedAt",
    "liked",
    "disLiked",
  ];
  const filteredObject = Object.fromEntries(
    Object.entries(val).filter(([key]) => !propertiesToRemove.includes(key))
  );
  let title = {
    introduce: "知识引入",
    explanation: "知识讲解",
    example: "例题",
    application: "知识点生活应用",
    summary: "总结",
    materials: "课前材料",
    teachingAim: "教学目标",
    advice: "课堂实施环节建议",
    homework: "作业",
    learningObjectives: "学习目标",
    knowledgePoint: "知识点内容梳理",
    practice: "练习题",
    practicalApplication: "实践应用",
  };
  if (val2.applyEffect) {
    //要打字机
    if (val2.askType === "TALK" && isJSON(val.reply)) {
      let obj = [];
      obj.push(JSON.parse(val.reply).content);
      // console.log('🐬-----obj-----', obj);

      const text = obj[0]
        .map((item) => {
          // console.log('🍪-----item-----', item );
          // if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          // if (Array.isArray(item)) {
          //   // 将数组的每个项用 <br> 分开
          //   const formattedArray = item.map(item => `${item}`).join('<br>');
          //   return `<strong style="color: #2d91f8;">${item.linkName}</strong><br>${formattedArray}`;
          // }
          return `&lt;strong style="color: #FF5E2F;">${item.linkName}</strong><br&gt;${item.linkContent}`;
          // }
        })
        .filter(Boolean)
        .join("&lt;br><br&gt;");
      return text;
    } else if (
      val2.askType === "COURSEWARE" &&
      val.reply &&
      isJSON(val.reply)
    ) {
      let obj = [];
      if (!val.reply) return;
      obj.push(JSON.parse(val.reply));
      const text = obj[0]
        .map((item) => {
          // console.log('🍪-----item-----', item );
          // if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          // if (Array.isArray(item)) {
          //   // 将数组的每个项用 <br> 分开
          //   const formattedArray = item.map(item => `${item}`).join('<br>');
          //   return `<strong style="color: #2d91f8;">${item.linkName}</strong><br>${formattedArray}`;
          // }
          return `&lt;strong style="color: #FF5E2F;">${item.title}</strong><br&gt;${item.text}`;
          // }
        })
        .filter(Boolean)
        .join("&lt;br><br&gt;");
      return text;
    }
    const combinedString = Object.entries(filteredObject)
      .map(([key, value]) => {
        if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          if (Array.isArray(value)) {
            // 将数组的每个项用 <br> 分开
            const formattedArray = value
              .map((item) => `${item}`)
              .join("&lt;br&gt;");
            return `&lt;strong style="color: #FF5E2F;">${title[key]}</strong><br&gt;${formattedArray}`;
          }
          return `&lt;strong style="color: #FF5E2F;">${title[key]}</strong><br&gt;${value}`;
        }
        return value ? `${value}` : "";
      })
      .filter(Boolean)
      .join("&lt;br><br&gt;");
    return combinedString;
  } else {
    if (val2.askType === "TALK" && isJSON(val.reply)) {
      let obj = [];
      obj.push(JSON.parse(val.reply).content);
      // console.log('🐬-----TALK-----', obj);

      const text = obj[0]
        .map((item) => {
          // console.log('🍪-----item-----', item );
          // if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          // if (Array.isArray(item)) {
          //   // 将数组的每个项用 <br> 分开
          //   const formattedArray = item.map(item => `${item}`).join('<br>');
          //   return `<strong style="color: #2d91f8;">${item.linkName}</strong><br>${formattedArray}`;
          // }
          return `<strong style="color: #FF5E2F;">${item.linkName}</strong><br>${item.linkContent}`;
          // }
        })
        .filter(Boolean)
        .join("<br><br>");
      return text;
    } else if (
      val2.askType === "COURSEWARE" &&
      val.reply &&
      isJSON(val.reply)
    ) {
      let obj = [];
      obj.push(JSON.parse(val.reply));
      // console.log('🌈-----val-----', val2);
      // console.log('🐬-----COURSEWARE-----', obj);
      const text = obj[0]
        .map((item) => {
          // console.log('🍪-----item-----', item );
          // if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          // if (Array.isArray(item)) {
          //   // 将数组的每个项用 <br> 分开
          //   const formattedArray = item.map(item => `${item}`).join('<br>');
          //   return `<strong style="color: #2d91f8;">${item.linkName}</strong><br>${formattedArray}`;
          // }
          return `<strong style="color: #FF5E2F;">${item.title}</strong><br>${item.text}`;
          // }
        })
        .filter(Boolean)
        .join("<br><br>");
      return text;
    }
    // 处理对象的每个键值对
    const combinedString = Object.entries(filteredObject)
      .map(([key, value]) => {
        if (title[key] && value) {
          // 如果是 'example' 并且 value 是数组，则处理数组的每一项
          if (Array.isArray(value)) {
            // 将数组的每个项用 <br> 分开
            const formattedArray = value.map((item) => `${item}`).join("<br>");
            return `<strong style="color: #FF5E2F;">${title[key]}</strong><br>${formattedArray}`;
          }
          return `<strong style="color: #2d91f8;">${title[key]}</strong><br>${value}`;
        }
        return value ? `${value}` : "";
      })
      .filter(Boolean)
      .join("<br><br>");
    return combinedString;
  }
};
function isJSON(str) {
  try {
    JSON.parse(str); // 尝试解析字符串
    return true; // 如果解析成功，则返回 true
  } catch (e) {
    return false; // 如果解析失败，则返回 false
  }
}
function isJSONType(type, val) {
  // console.log('🎉---isJSONType--val--7---', val);
  if (!val || isJSON(val)) {
    return true;
  } else {
    return false;
  }
}
function isIterable(obj) {
  return obj != null && typeof obj[Symbol.iterator] === "function";
}
const rendering = (val, val2) => {
  // console.log('🐳-----val-----', dataHandle2(val));
  return dataHandle2(val, val2);
};
let textTimer = null; // 新增定时器用于文本切换
let textIndex = 0; // 当前文本索引
const Switchtxt = ref([
  "正在分析知识点...",
  "正在收集教学素材...",
  "正在构建课件大纲...",
  "正在解构课件...",
  "正在设计知识引入...",
  "正在优化文案内容...",
  "正在检查内容准确性...",
  "正在进行最后完善...",
]);
const currentText = ref("");
const switchText = ref(false); // 控制是否显示文本
let index = 0;

// 控制文本切换的函数
const startTextSwitch = () => {
  let index = 0;
  // 4 秒后开始显示文本
  setTimeout(() => {
    switchText.value = true; // 显示文本
    currentText.value = Switchtxt.value[textIndex]; // 显示第一个文本
  }, 4000); // 等待 4 秒后开始显示第一个文本
  // 4 秒后开始每隔 5 秒切换一次文本
  textTimer = setInterval(() => {
    currentText.value = Switchtxt.value[index];
    if (index < Switchtxt.value.length - 1) {
      index++;
    } else {
      clearInterval(textTimer);
    }
    console.log("🌵-----index-----", index);
    // console.log('🐠-----currentText.value-----', currentText.value);
    // textIndex = (textIndex + 1) % Switchtxt.value.length;
    // currentText.value = Switchtxt.value[textIndex];  // 更换文本
  }, 5000); // 每隔 5 秒切换一次
};
// 清理文本切换定时器
const clearTextSwitch = () => {
  clearTimeout(textTimer);
  clearInterval(textTimer);
};

// 当点击实验模型选项时
const selectedIng = ref(false);
// 当"启用读书郎..."在内部
// const handleCheckboxChange = (selectedItem) => {
// if(selectedItem.value){
//   if (selectedItem.list_name === '启用读书郎实验室模型算法' ) {
//     Tabtepm.value.list.forEach(item => {
//       if (item.list_name !== '启用读书郎实验室模型算法') {
//         item.value = false
//         item.disabled = true  // 设置其他选项为禁用
//       }
//     })
//   }
// } else {
//     if (selectedItem.list_name === '启用读书郎实验室模型算法' ) {
//       Tabtepm.value.list.forEach(item => {
//         if (item.list_name !== '启用读书郎实验室模型算法') {
//           item.value = true;
//           item.disabled = false
//         }
//       })
//     }
//   }
// }

// const handleCheckboxChanges = () => {
//   const itemsToDisable = ['知识引入', '知识讲解', '例题', '知识点生活应用', '总结', '课前材料'];
//   if (selectedIng.value) {
//     Tabtepm.value.list.forEach(item => {
//       if(Tabtepm.value.name =='课件'){
//           if (itemsToDisable.includes(item.list_name)) {
//           item.value = false;
//           item.isDisabled = true;
//        }
//       }
//     });
//   } else {
//     Tabtepm.value.list.forEach(item => {
//       if(Tabtepm.value.name =='课件'){
//         if (itemsToDisable.includes(item.list_name)) {
//         item.value = true;
//         item.isDisabled = false;
//         }
//       }
//     });
//   }
// };

// 当"启用读书郎..."在外部
const handleCheckboxChanges = () => {
  const itemsToDisable = [
    "知识引入",
    "知识讲解",
    "例题",
    "知识点生活应用",
    "总结",
    "课前材料",
  ];
  const shouldDisable = selectedIng.value;
  if (Tabtepm.value.name === "课件") {
    Tabtepm.value.list.forEach((item) => {
      if (itemsToDisable.includes(item.list_name)) {
        item.value = !shouldDisable;
        item.isDisabled = shouldDisable;
      }
    });
  }
};

const renderingx = (ERROR = "") => {
  // console.log('🐳-----val-----',ERROR);
  // if(!ERROR) return true
  if (ERROR.includes("ERROR")) {
    console.log("🥠请求失败，请稍后再试！");
    masgArr.value[masgArr.value.length - 1].tip = true;
    // masgArr.value[masgArr.value.length - 1].errMsg = true;
    return true;
  } else {
    return false;
  }
};
const Imgshan = ref(false);
const count = ref(0); // 计数器，用于统计 ** 出现次数
const hashCount = ref(0); // 计数器，用于统计 # 出现次数
const typesetx = ref("");

const processExperimentReply = (reply) => {
  // console.log('🍭-----reply-----', reply);
  // if (reply + '' == "undefined") return
  if (!reply || typeof reply !== "string") return;
  const lines = reply.split("\n");
  let result = "";

  for (const line of lines) {
    if (line.startsWith("很") || line.startsWith("抱")) {
      const indexOfLai = line.indexOf("：");
      if (indexOfLai !== -1) {
        result += line.substring(indexOfLai + 1).trim() + "\n";
      }
      // 处理完以"很\抱"开头的行后，跳过继续
      continue;
    }
    // 添加未被处理的行
    result += line + "\n";
  }
  return result.trim(); // 去除最后的换行符
};

const katexOptions = {
  throwOnError: false, // 遇到错误是不抛出异常 继续渲染
  displayMode: true, // 使用显示模式 公式居中并换行
  strict: false, // 关闭严格模式 允许非标准的 LaTeX 语法
};
// 历史
const katexOptions2 = {
  throwOnError: true,
  displayMode: false,
  strict: true,
};
// 生成ppt后下载
const Downloadppt = (exportfile) => {
  if (!exportfile.url) {
    ElMessage.error("下载失败：地址不存在");
    return;
  }

  // 显示"下载中"提示
  const loadingMessage = ElMessage.info("下载中...");

  downloadFile(exportfile.url, exportfile.fileName, {
    onSuccess: (response) => {
      // 关闭"下载中"提示
      loadingMessage.close();
      ElMessage.success("下载完成！");
      console.log("下载成功：", response);
    },
    onError: (error) => {
      // 关闭"下载中"提示
      loadingMessage.close();
      ElMessage.error("下载失败");
      console.error("下载失败：", error);
    },
  });
};
// const downloadFile = (url, fileName) => {
//   console.log(url, fileName);
//   const link = document.createElement("a");
//   link.href = url;
//   link.download = fileName;
//   link.style.display = "none";
//   document.body.appendChild(link);
//   link.click();
//   document.body.removeChild(link);
// };

// const downloadFile = (url, fileName = "未知文件") => {
//   let xhr = new XMLHttpRequest();
//   console.log("🎉-----xhr-----", xhr);
//   xhr.open("get", url, true);
//   // 请求类型
//   xhr.responseType = "blob";
//   xhr.onreadystatechange = function () {
//     // 获取接口结果
//     if (xhr.readyState === 4 && xhr.status === 200) {
//       window.URL = window.URL || window.webkitURL;
//       let a = document.createElement("a");
//       let blob = new Blob([xhr.response]);
//       // 通过二进制文件创建url
//       let url = window.URL.createObjectURL(blob);
//       a.href = url;
//       a.download = fileName;
//       ElMessage.success("下载模版成功");
//       a.click();
//       // 销毁创建的url
//       window.URL.revokeObjectURL(url);
//     } else {
//       if (xhr.readyState === 1) {
//         ElMessage.error("下载模版失败");
//       }
//     }
//   };
//   xhr.send();
// };

const escapeSpecialChars = (input) => {
  //删除Img标签、转义百分号、转义- -转化为uI
  const imgTagRegex = /<img[^>]*>/g;
  return input
    .replace(imgTagRegex, "")
    .replace(/<ul>\s*<li>(.*?)<\/li>\s*<\/ul>/g, "<span>-$1</span>");
};
const decodeHtmlEntities = (str) => {
  const txt = document.createElement("textarea");
  txt.innerHTML = str;
  return txt.value;
};
const htmlContent = (val) => {
  // console.log('🌵-----val-----', val);
  let html = marked(val);
  html = escapeSpecialChars(html);
  // console.log('🦄-----html-----', html);
  const renderMath = (math, options) => {
    try {
      return katex.renderToString(math, options);
    } catch (err) {
      // console.error(err); // Log the error for debugging purposes
      return `<span class="katex-error">${math}</span>`; // Show original math on error
    }
  };

  // 优先处理 \left(...\right) 形式
  html = html.replace(
    /\(\\left\(([\s\S]+?)\\right\)\)/g,
    (match, innerText) => {
      // console.log('🌳-----match-----', match);
      const decodedMathContent = decodeHtmlEntities(innerText.trim());
      return renderMath(decodedMathContent, { ...katexOptions2 });
    }
  );

  // 处理行内 $...$ 形式
  html = html
    .replace(/\$\$/g, "$")
    .replace(/\$(.*?)\$/gs, (match, inlineDollarMath) => {
      // console.log('🌈-----inlineDollarMath-----', inlineDollarMath);
      // 解码 HTML 实体，获取公式内容
      const mathContent = decodeHtmlEntities(inlineDollarMath.trim());

      // 替换英文括号为中文括号
      const protectedMath = mathContent
        .replace(/%/g, "\\%") // 在 % 前面添加 \
        .replace(/\(/g, "( ") // 在 `(` 后添加空格
        .replace(/\)/g, " )"); // 在 `)` 前添加空格
      // .replace(/\[/g, '[ ') // 在 `[` 后添加空格
      // .replace(/\]/g, ' ]'); // 在 `]` 前添加空格
      // console.log('🐠-----protectedMath-----', protectedMath);

      // 渲染公式
      return renderMath(protectedMath, { ...katexOptions2 });
    });

  html = html.replace(/\\begin\{([^}]+)\}[\s\S]*?\\end\{\1\}/g, (match) => {
    if (/<[^>]*>/.test(match)) {
      return match; // 跳过这些内容
    }

    // 打印匹配到的内容以供调试

    // 清理 HTML 转义字符（如有）
    const cleanContent = match
      .replace(/&amp;/g, "")
      .replace(/&gt;/g, "")
      .replace(/\(/g, "( ") // 在 `(` 后添加空格
      .replace(/\)/g, " )") // 在 `)` 前添加空格;
      .replace(
        /\\(?!begin|end|frac|Delta|pm|times|sqrt|over|under|int|sum|prod|lim|log|ln|sin|cos|tan|cot|sec|csc|arg|deg|det|dim|exp|gcd|hom|ker|inf|lim|max|min|Pr|sup)/g,
        "\\\\"
      ) // 替换 \，但跳过 \begin 和 \end
      .replace(/%/g, "\\%"); // 在 % 前面添加 \

    // 删除 \begin{cases} 前面的 [ 或 ( 符号
    const cleanedContent = cleanContent
      .replace(/^\s*[(\[]/, "")
      .replace(/[)\]]\s*$/, "");
    // console.log('🌟 Matching Content:', match);

    // 直接返回带有 \begin{align*} 和 \end{align*} 的完整公式
    return renderMath(cleanedContent.trim(), { ...katexOptions });
  });

  // 匹配外层用 () 包裹起来的内容，但不包含 \left 或 HTML 标签
  html = html.replace(/\((?:[^()]*\([^()]*\))*[^()]*\)/g, (match) => {
    if (/\\left|<[^>]*>/.test(match)) {
      return match; // 跳过这些内容
    }
    if (/<[^>]*>/.test(match)) {
      return match; // 跳过这些内容
    }
    // 检查是否包含 \begin 或 \end
    if (/\\begin|\\end/.test(match)) {
      // console.log('🦄-----match-----', match);
      return match; // 跳过这些内容
    }
    if (match.includes("( ") || match.includes(" )")) {
      return match;
    }
    // console.log('🌵-----match-----', match);

    const mathContent = decodeHtmlEntities(match.slice(1, -1).trim()); // 去掉外层括号并解码内容
    return renderMath(mathContent, { ...katexOptions2 }); // 渲染数学公式
  });

  return html
    .replace(/<strong>/g, '<strong class="custom-strong" style="">')
    .replace(/<\/strong>/g, "</strong>");
};

// var marked = require('marked');
const chatAi = async (content, accessToken, end) => {
  try {
    let postData = {
      description: content.description, //描述
      askType: content.askType,
      experiment: "true",
      contentType: content.contentType,
    };
    console.log("🌵-----encrypt(postData)-----", encrypt(postData));
    console.log("🐳-----postData-----", postData);
    const { nonce, timestamp, sign } = encrypt(postData);
    // console.log('🎁-----baseUrl-----', baseUrl);
    // return
    const response = await fetch(`${baseUrl.apiServer}api/question/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `${accessToken}`,
        // 'timestamp': timestamp,
        // 'nonce':nonce,
        // 'sign':sign
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 读取流数据
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        // end();
        Imgshan.value = false;
        resetLoading.value = false;
        // 保留用户输入，不清空输入框
        // Tabtepm.value.textareavalue = "";
        // masgArr.value[masgArr.value.length - 1].id = cleanValue.split('=')[1]

        setTimeout(async () => {
          const res = await findAll({
            userId: userId.value,
            page: 0,
            size: 10,
          });
          console.log("🌵-----res-----", res.data.content);
          let data = res.data.content[0]?.answerDTOS[0]?.experimentReply;
          masgArr.value[
            masgArr.value.length - 1
          ].answerDTOS[0].experimentReply = data;
          console.log(
            "🍭----- masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply-----",
            masgArr.value[masgArr.value.length - 1].answerDTOS[0]
              .experimentReply
          );
          await nextTick();
          scrollToBottom(); // 首次加载，滚动到底部
        }, 500);

        break;
      }
      switchText.value = true;
      Imgshan.value = true;
      currentText.value = "";
      const decodedValue = decoder.decode(value, { stream: true });
      let cleanValue = decodedValue
        .replace(/data:\s*/g, "")
        .replace(/undefined/g, "")
        .replace(/\n/g, ""); // 去掉前缀
      // console.log('🌵-----cleanValue-----', cleanValue.split(''));
      if (cleanValue.includes("[DONE]questionId")) {
        masgArr.value[masgArr.value.length - 1].id = cleanValue.split("=")[1];
        // console.log('🐠-----masgArr.value[masgArr.value.length - 1].id-----', masgArr.value[masgArr.value.length - 1].id);
        Imgshan.value = false;
        resetLoading.value = false;
        // 保留用户输入，不清空输入框
        // Tabtepm.value.textareavalue = "";
        scrollToBottom();
        // return
      }
      let typesetx = cleanValue;
      // console.log('🍪-----cleanValue-----', cleanValue);
      // typesetx = cleanValue.replaceAll('-', '<br>·').replace(/\s+/g, '').replace(/\[DONE\]questionId=\d+/g, '').replace(/undefined/g, '')
      typesetx = cleanValue.replace(/\[DONE\]questionId=\d+/g, "");

      //       typesetx = cleanValue.replace(/\s+/g, '').replace(/\[DONE\]questionId=\d+/g, '').replace(/undefined/g, '')

      typesetx = typesetx.replace(/(\*\*|#)/g, (match) => {
        if (match === "**") {
          count.value++;
          // 如果是奇数次出现，插入加粗的开标签
          if (count.value % 2 !== 0) {
            // return `<br><br><strong style="color: #2d91f8;">`;
            return `<br><br><strong style="">`;
          }
          // 如果是偶数次出现，插入加粗的闭标签
          return `</strong>`;
        }

        if (match === "#") {
          // 遇到 # 时直接插入换行
          return `<br>`;
        }
      });

      masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply +=
        typesetx; // 累加清理后的值
      // console.log('🐳-----masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply-----', masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply);
      scrollToBottom();
    }
  } catch (error) {
    // console.error("🥠请求失败：", error);
  }
};

//WebSocket 版本
const { status, data, send, open, close, ws } = useWebSocket(
  baseUrl.wsApiServer,
  {
    // protocols: [easyToken.value],
    onMessage: wsOnMessage,
    onError: wsonError,
    autoReconnect: true,
    onDisconnected: function (ws, event) {
      console.log("已断开连接");
      // 尝试重新连接
      // setTimeout(() => {
      //   console.log("尝试重新连接...");
      //   open();
      // }, 5000); // 等待5秒后重新连接
    },
  }
);

// const socket = new WebSocket(baseUrl.wsApiServer,[easyToken.value]);

// socket.addEventListener('open', (event) => {
//   console.log('🍧-----event--********---', event);
//   let postData = {
//       jsonMsg: {
//         description:'春晓', //描述
//         askType: 'COURSEWARE',
//         experiment: "true",
//         contentType: [],
//         // userId: 2
//       },
//       type: "chat",
//       // auth: easyToken.value
//     };
//   // 在握手阶段通过自定义协议传递Authorization信息
//   const sendCon = JSON.stringify(postData);
//   socket.send(sendCon);
// });

// socket.addEventListener('message', (event) => {
//   // 处理接收到的消息
//   console.log('WebSocketReceived/:', event.data);
// });
// socket.addEventListener('message', (event) => {
//   // 处理接收到的消息
//   console.log('WebSocketReceived/:', event.data);
// });

watch(status, (newVal) => {
  console.log("🎉-----newVal-----", newVal);
  // console.log('🍭-----baseUrl.wsApiServer-----', baseUrl.wsApiServer);
  if (status == "OPEN") {
    webSocketState.value = true;
  } else if (status == "CLOSED") {
    webSocketState.value = false;
  }
  console.log(
    "🎁-----status-----",
    status,
    status == "OPEN" ? "打开" : status == "CLOSED" ? "关闭" : "连接中"
  );
});

let pendingMessage = ""; // 用于存储待渲染的消息内容
// let count = { value: 0 }; // 用于记录'**'出现的次数

// function wsOnMessage(ws, msgCo) {

//   let newData = "";
//   switchText.value = true;
//   Imgshan.value = true
//   const { data = "" } = msgCo || {};
//   newData = JSON.parse(data).jsonMsg;
//   // console.log('🎉-----newData-----', newData);

//   if (JSON.parse(data).type === "end") {
//     Imgshan.value= false
//     Tabtepm.value.textareavalue = "";
//     resetLoading.value = false; // 响应状态完成
//     const questionId = JSON.parse(JSON.parse(data).jsonMsg).questionId; // 问题Id
//     masgArr.value[masgArr.value.length - 1].id = questionId;
//     scrollToBottom();
//   } else {
//     console.log('🐠-----newData-----', newData.split(''));

//     if (newData.includes("**")) {
//       count.value++;
//       // 如果是奇数次出现，不渲染，存储消息内容
//       if (count.value % 2 !== 0) {
//         pendingMessage += newData;
//       } else {
//         // 如果是偶数次出现，渲染存储的消息内容和当前消息
//         pendingMessage += newData;
//         console.log('🍧-----pendingMessage-----', pendingMessage);
//         masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply +=  pendingMessage;
//         pendingMessage = ""; // 清空待渲染消息内容
//         scrollToBottom();
//       }
//     } else {
//       // 如果消息内容不包含"**"，直接渲染
//       if (pendingMessage) {
//         pendingMessage += newData;
//       } else {
//         masgArr.value[masgArr.value.length - 1].answerDTOS[0].experimentReply +=  newData;
//         scrollToBottom();
//       }
//     }
//   }
// }
const forceScrollToBottom = () => {
  setTimeout(() => {
    scrollToBottom2();
  }, 10);
};
const ppxx = ref(false);
const disabledfx = ref(false);
const thinkingStatus = ref("thinking"); // 添加思考状态变量

function wsOnMessage(ws, msgCo) {
  clearTextSwitch();
  let newData = "";
  switchText.value = true;
  Imgshan.value = true;
  ppxx.value = true;
  disabledfx.value = true;
  currentText.value = "";
  // 实验及设计
  openImg3.value = false;
  const { data = "" } = msgCo || {};
  const parsedData = JSON.parse(data);
  newData = parsedData.jsonMsg;
  const messageType = parsedData.type; // 获取消息类型

  const messageObject = masgArr.value[masgArr.value.length - 1];
  const answerObject = messageObject.answerDTOS[0];

  if (parsedData.type === "end") {
    Imgshan.value = false;
    resetLoading.value = false;
    const questionId = JSON.parse(newData).questionId;
    messageObject.id = questionId;
    forceScrollToBottom();
    if (openindex.value.openindex) {
      masgArr.value[openindex.value.index1].answerDTOS[0].courseQuestionId =
        questionId;
      console.log(
        "🎁----- masgArr.value[openindex.value.index1+1]-----",
        masgArr.value[openindex.value.index1 + 1]
      );
    }
    openloading.value = false;
    openindex.value.openindex = false;
    removeBlinkingDot(answerObject);
    ppxx.value = false;
    disabledfx.value = false;
  } else {
    removeBlinkingDot(answerObject);

    // 根据消息类型分别处理
    if (messageType === "rc") {
      // 处理深度思考内容
      if (!messageObject.thinkingContent) {
        messageObject.thinkingContent = "";
        messageObject.thinkingStatus = "thinking"; // 初始化状态为 thinking
      }
      messageObject.thinkingContent += newData;
    } else if (messageType === "chat") {
      // 当收到 chat 类型消息时，如果存在 thinkingContent，将状态设为 end
      if (messageObject.thinkingContent) {
        messageObject.thinkingStatus = "end";
      }
      // 更新消息，并重置点
      if (newData.includes("**")) {
        count.value++;
        if (count.value % 2 !== 0) {
          pendingMessage += newData;
        } else {
          pendingMessage += newData;
          answerObject.experimentReply += pendingMessage;
          pendingMessage = "";
        }
      } else {
        if (pendingMessage) {
          pendingMessage += newData;
        } else {
          answerObject.experimentReply += newData;
          appendBlinkingDot(answerObject);
        }
      }
      scrollToBottom2();
    }
  }
}

function appendBlinkingDot(messageObject) {
  // 确保只有一个闪烁点在末尾
  messageObject.experimentReply += '<span class="blinking-dot">●</span>';
}

function removeBlinkingDot(messageObject) {
  // 移除任何未结束的 ·
  messageObject.experimentReply = messageObject.experimentReply.replace(
    /<span class="blinking-dot">●<\/span>$/,
    ""
  );
}

function wsonError(ws, msgCo) {
  console.log("🎉-----ws, msgCo-----", ws, msgCo);
  console.log("长链接失败");
}
// const handelSend = () => {
//   const msg = {
//     event: "joinRoom",
//   };
//   const sendCon = JSON.stringify({"jsonMsg":"{\"description\":\"背影\",\"experiment\":true}","type":"chat"});
//   send(sendCon);
// };

const responseQueue = ref("");
const webSocketState = ref(false); // webSocket 状态
const responseState = ref(false); // webSocket 响应状态

const chatAi2 = async (content, accessToken, isd, end) => {
  console.log("🍪-----isd-----", isd.value);
  console.log("🍧--传入---content-----", content);
  responseQueue.value = ""; //清空队列
  // 清空深度思考内容
  // thinkingContent.value = "";
  console.log("🍭-----webSocketState-----", webSocketState.value);
  if (webSocketState.value) {
    return console.log("连接中");
  }
  try {
    let postData = {
      userId: userId.value,
      jsonMsg: content.description,
      type: content.askType,
      isDeepSeek: isDeepThinking.value, // 添加isDeepSeek参数，默认为false
    };

    console.log("🌵-----encrypt(postData)-----", encrypt(postData));
    console.log("🐳-----postData-----", postData);
    // const { nonce, timestamp, sign } = encrypt(postData);
    // console.log('🎁-----baseUrl-----', baseUrl);
    //     {"jsonMsg":"提问json对象字符串","type":"chat"}
    // {"jsonMsg":"{\"description\":\"春晓\",\"askType\":\"0\",\"experiment\":true}","type":"chat"}
    const sendCon = JSON.stringify(postData);
    console.log("🍪-----sendCon-----", sendCon);
    send(sendCon);
  } catch (error) {
    console.error("请求失败：", error);
  }
};

const showBackBottom = ref(false);

// 处理回到底部按钮点击
const handleBackToBottom = () => {
  scrollToBottom();
  showBackBottom.value = false;
};
</script>

<template>
  <!-- <div>{{buffer}}</div> -->
  <div class="con_content">
    <div class="AI-content">
      <div class="AI-left">
        <div class="Bghead">
          <img :src="platformIcon" alt="" />
          <span>AI 课程大师</span>
        </div>
        <div style="color: black" class="textY">我们可以怎么帮助您？</div>
        <div class="item-listBox">
          <template v-for="(item, index) in Tablist" :key="item.tab">
            <div
              class="item-list"
              :class="{ 'active-class': item.state }"
              @click="setActive(item, index)"
            >
              {{ item.name }}
            </div>
          </template>
        </div>
        <div class="AI-BottomBox">
          <div class="AI-Bottom">
            <div class="textY">{{ Tabtepm.TabTitle }}</div>
            <!-- input 输入 -->
            <div class="inputlis" v-if="Tabtepm.type == 'input'">
              <el-input
                v-model="sharedTextareavalue"
                :maxlength="Tabtepm.Maxlength"
                :placeholder="Tabtepm.placeholder"
                show-word-limit
                type="textarea"
                resize="none"
                :rows="7"
                class="custom-placeholder"
              />
              <div class="deep-thinking-btn-container">
                <el-tooltip
                  content="开启后将使用Deepseek模型生成内容，更深度的推理过程，生成所需时间更长"
                  placement="bottom"
                  :show-after="200"
                >
                  <div
                    class="deep-thinking-btn"
                    :class="{ active: isDeepThinking }"
                    @click="isDeepThinking = !isDeepThinking"
                  >
                    <span>{{ "深度思考" }}</span>
                  </div>
                </el-tooltip>
              </div>
              <div class="checklist">
                <!--  @change="handleCheckboxChange(item)" -->
                <el-checkbox
                  v-for="(item, index) in Tabtepm.list"
                  :key="index"
                  v-model="item.value"
                  :label="item.list_name"
                  :disabled="item.isDisabled"
                />
              </div>
            </div>
            <!-- 下拉、搜索、难度 -->
            <div class="selectlis">
              <div v-if="Tabtepm.type == 'select'">
                <el-select
                  v-model="Tabtepm.Tepmlable.selectvalue"
                  placeholder="请选择"
                  size="large"
                >
                  <el-option
                    v-for="item in Tabtepm.option"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <div class="Binput">
                  <el-input
                    v-model="Tabtepm.Tepmlable.searchvalue"
                    size="large"
                    :placeholder="Tabtepm.placeholder"
                  >
                    <template #prefix>
                      <el-icon class="el-input__icon">
                        <search />
                      </el-icon>
                    </template>
                  </el-input>
                  <el-button type="primary" class="appdsearch">搜索</el-button>
                </div>
                <div class="Cdemon">
                  <span class="demonstrationx">难度</span>
                  <el-rate v-model="Tabtepm.Tepmlable.Ratevalue" size="large" />
                </div>
              </div>
            </div>
          </div>
          <div class="Calsub">
            <div>
              <!-- <input type="text" id="message" placeholder="Enter message">
              <button @click="sendMessage">SSE提交</button> -->
            </div>
            <div class="couIng">
              <el-checkbox
                v-if="Tabtepm.emun == 'COURSEWARE'"
                v-model="selectedIng"
                label="启用读书郎实验室模型算法"
                size="large"
                @change="handleCheckboxChanges"
                :disabled="disabledfx"
              />
            </div>
            <el-button
              type="primary"
              @click="submit()"
              class="Calsubmit"
              :loading="resetLoading"
              :style="{ backgroundColor: isInputValid ? '' : '#FFB59A' }"
              >开始课程设计</el-button
            >
          </div>
        </div>
      </div>
      <!-- {{ppxx}} -->
      <div class="AI-right" ref="chatContainer" @scroll="onScroll">
        <div class="content" v-for="(item, index) in masgArr" :key="index">
          <div class="description">
            <img class="head" :src="avatar" alt="" />
            <div class="text">
              {{ item.description }}
            </div>
          </div>
          <div class="reply">
            <!-- 历史消息 -->
            <img class="head" :src="platformIcon" alt="" />
            <!-- v-if="!item2.reply && errMsg" -->
            <div
              class="reply_right"
              :class="{ reply_right_err: item.tip }"
              v-for="item2 in item.answerDTOS"
              :key="item2.id"
            >
              <Thinking
                v-if="item.thinkingContent || item.reasoningContent"
                :content="item.thinkingContent || item.reasoningContent"
                :status="
                  item.reasoningContent
                    ? 'end'
                    : item.thinkingStatus || 'thinking'
                "
                buttonWidth="200px"
                background-color="#E5E7EB"
                max-width="100%"
              />
              <div class="text_ai">
                <!-- 空状态 -->
                <!-- <span class="None" v-if="(processExperimentReply(item2.experimentReply) == '' && item.id)">
                  这段内容似乎较为混乱，缺乏明确的主题和逻辑。我们可以换一种方式进行提问。
                </span> -->
                <!-- 实验模型及行程安排loading动画 -->
                <div
                  v-if="
                    (item.askType == 'COURSEWARE' && selectedIng) ||
                    item.askType == 'INSTRUCTIONAL_DESIGN'
                  "
                >
                  <!-- {{ ppxx }} -->
                  <!-- {{ item.askType }} -->
                  <!-- {{ !item.id }}1 -->
                  <!-- {{ Twotabloading }}2 -->
                  <!-- {{!item.id && Twotabloading}} 第一版-->
                  <img
                    v-if="
                      !item.id && !processExperimentReply(item2.experimentReply)
                    "
                    class="animeAiloading"
                    :src="chatLoading"
                    alt=""
                  />
                </div>
                <!-- {{ !item.id }}1
                {{ (item2.experimentReply == '') }} -->
                <!-- {{ openImg3 && !item.id}} -->
                <img
                  v-if="openImg3 && !item.id"
                  class="animeAiloading"
                  :src="chatLoading"
                  alt=""
                />

                <!-- <div v-if=" Tabtepm.value.name === '课件'">********</div> -->
                <div
                  class="text_ai_content"
                  v-if="item.askType == 'COURSEWARE'"
                >
                  <!-- <div v-if="item2.experiment">{{ item2.experimentReply }}</div> -->
                  <div
                    class="history"
                    v-if="item2.experimentReply"
                    v-html="
                      htmlContent(processExperimentReply(item2.experimentReply))
                    "
                  ></div>
                  <div
                    v-else
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                </div>
                <div
                  class="text_ai_content"
                  v-if="item.askType == 'EXAMINATION'"
                >
                  <div
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                </div>
                <div class="text_ai_content" v-if="item.askType == 'HANDOUTS'">
                  <div
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                </div>
                <div class="text_ai_content" v-if="item.askType == 'ITINERARY'">
                  <div
                    class="history"
                    v-if="item2.experimentReply"
                    v-html="
                      htmlContent(processExperimentReply(item2.experimentReply))
                    "
                  ></div>
                  <div
                    v-else
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                </div>
                <div class="text_ai_content" v-if="item.askType == 'TALK'">
                  <img
                    class="animeAiloading"
                    v-if="
                      !switchText &&
                      !item2.reply &&
                      item.applyEffect &&
                      !item.errMsg
                    "
                    :src="chatLoading"
                    alt=""
                  />
                  <!-- {{!item2.reply}}--{{ switchText  }}--{{ item.applyEffect  }}--{{ !item.errMsg }}--{{ !currentText }} -->
                  <div
                    v-if="
                      !item2.reply &&
                      switchText &&
                      item.applyEffect &&
                      !item.errMsg
                    "
                  >
                    <span>{{ currentText }}</span>
                  </div>
                  <div class="err_img" v-if="!item2.reply && item.errMsg">
                    <img :src="err" alt="" />
                    <div>
                      <p>哎呀，数据加载失败了。</p>
                      <p>
                        请检查您的网络连接或<span
                          class="tip"
                          @click="submit2(item.description, index)"
                          >稍后再试</span
                        >。
                      </p>
                    </div>
                  </div>
                  <!-- 修改这里，对历史聊天记录的reply使用v-html和htmlContent进行渲染 -->
                  <div
                    v-if="item2.reply && !item.applyEffect"
                    class="history"
                    v-html="htmlContent(item2.reply)"
                  ></div>
                  <div
                    v-else-if="item2.reply"
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                  <div class="err_img" v-if="renderingx(item2.experimentReply)">
                    <img :src="err" alt="" />
                    <div>
                      <p>哎呀，数据加载失败了。</p>
                      <p>
                        请检查您的网络连接或<span
                          class="tip"
                          @click="submit2(item.description, index, 'Errors')"
                          >稍后再试</span
                        >。
                      </p>
                    </div>
                  </div>
                  <!-- <div v-if="item2.experimentReply"> -->
                  <img
                    class="animeAiloading"
                    :src="chatLoading"
                    alt=""
                    v-if="
                      !item.errMsg &&
                      !processExperimentReply(item2.experimentReply) &&
                      ppxx &&
                      !item2.reply
                    "
                  />
                  <!-- 实时消息 -->
                  <div style="display: inline-block">
                    <span
                      class="span_content"
                      v-if="item2.experimentReply"
                      v-html="
                        htmlContent(
                          processExperimentReply(item2.experimentReply)
                        )
                      "
                    >
                    </span>
                    <!-- </div> -->
                  </div>
                </div>
                <div
                  class="text_ai_content"
                  v-if="item.askType == 'TEACHING_PLAN'"
                >
                  <div
                    v-type="{
                      copyValue: `${rendering(item2, item)}`,
                      applyEffect: item.applyEffect ? item.applyEffect : false,
                      callback: handleComplete,
                    }"
                  ></div>
                </div>
              </div>
              <el-divider v-if="item.id" style="margin-bottom: 0" />
              <div class="text_bottom" v-if="item.id">
                <div class="function_container">
                  <!-- <el-button
                    class="botton_left"
                    v-show="isJSONType(item.askType, item2.reply)"
                    type="primary"
                    @click="open2(item, item2, index, index2)"
                  > -->
                  <!-- {{ askType(item2.experimentReply ? "COURSEWARE" : item.askType, item2.url) }} -->
                  <!-- {{ askType(item.askType, item2.url) }}
                  </el-button> -->
                  <div>
                    <!-- <el-button
                      v-if="item.askType == 'ITINERARY'"
                      class="botton_left"
                      type="primary"
                      @click="open3(item, item2, index, index2)"
                      :disabled="item2?.courseQuestionId ? true : false"
                      :loading="openloading"
                      :class="{ 'disabled-btn': item2.courseQuestionId }"
                    >
                      生成课件
                    </el-button> -->
                    <!-- {{ openloading }} -->
                    <!-- {{ item2?.courseQuestionId }} -->
                  </div>
                  <span style="margin-bottom: 10px"
                    >AI生成内容仅供参考，请注意甄别</span
                  >
                  <!-- 复制 -->
                  <el-button
                    class="botton_right"
                    @click="copyText(item2, item)"
                  >
                    复制
                  </el-button>
                  <!-- <el-button
                    v-if="item2.url"
                    class="botton_right"
                    @click="Downloadppt(item2)"
                  >
                    下载
                  </el-button> -->
                </div>
                <!-- <div class="isLiked">
                  <img
                    :src="item2.liked ? good2 : good"
                    @click="give(item, item2, index, index2)"
                    alt=""
                  />
                  <img
                    :src="item2.disLiked ? nogood2 : nogood"
                    @click="give2(item, item2, index, index2)"
                    alt=""
                  />
                </div> -->
              </div>
            </div>
          </div>
        </div>

        <!-- 添加回到底部按钮 -->
        <div
          class="back-to-bottom"
          @click="handleBackToBottom"
          v-show="showBackBottom"
        >
          <el-icon><ArrowDown /></el-icon>
        </div>
      </div>
      <Popup
        class=""
        :updatedAtTime="updatedAtTime"
        :condition="condition"
        :setPopu="setPopu"
        @setCenterDialogVisible="setCenterDialogVisible"
        :centerDialogVisible="centerDialogVisible"
      ></Popup>
    </div>
  </div>
</template>

<style lang="less" scoped>
// 导入主题配色文件
@import "~/styles/theme.less";

// @import "https://cdn.jsdelivr.net/npm/katex@0.10.1/dist/katex.min.css";
// :deep(.katex) {
//   display: inline-block  !important;
//   overflow: hidden !important;
// }
// .katex-html {
//   display: none;
// }
.con_content {
  width: 100%;
  // height: 500px;
  height: calc(100vh - 51px);
  padding-left: 10px;
  padding-right: 5px;
  box-sizing: border-box;
  // transform: translate(-50%, -50%);
}

.deep-thinking-btn-container {
  margin-top: 8px;
}

.deep-thinking-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 28px;
  padding: 0 12px;
  border-radius: @border-radius-md;
  background-color: @bg-primary;
  color: @text-tertiary;
  font-size: @font-size-sm;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.deep-thinking-btn:hover {
  background-color: @bg-quaternary;
}

.deep-thinking-btn.active {
  background-color: @brand-primary;
  color: @text-inverse;
}

:deep(.custom-strong) {
  font-size: @font-size-sm;
  // margin-left: 6px;
  // color: @info-color;
}

// 实时
// 总标题
:deep(.span_content h1) {
  font-size: @font-size-lg;
}

:deep(h2) {
  font-size: @font-size-md;
  color: @brand-primary;
}

:deep(h3) {
  font-size: @font-size-sm;
}

:deep(.span_content > p > strong) {
  color: @brand-primary;
}

// 幻灯片
:deep(.span_content p) {
  font-size: @font-size-sm;
  color: @text-primary;

  .katex-html {
    svg {
      width: 20px;
    }
  }
}

// 行程安排
:deep(.span_content > h1) {
  font-size: 13px;
  color: @brand-primary;
}

// 正文
:deep(.span_content > ul) {
  margin-top: -10px;
  margin-left: -8px;
}

:deep(.span_content > ul > li > ul) {
  margin-left: -8px;
}

:deep(.span_content li) {
  font-size: 11px;

  .katex {
    svg {
      width: 20px;
    }
  }

  .katex-html {
    display: none;
  }
}

// 历史
// 总标题
:deep(.history h1) {
  font-size: @font-size-lg;
}

:deep(.history > p > strong) {
  color: @brand-primary;
}

// 幻灯片
:deep(.history p) {
  font-size: 11px;
  color: @text-primary;

  .katex-html {
    svg {
      width: 20px;
    }
  }
}

// 行程安排
:deep(.history > h1) {
  font-size: @font-size-sm;
  color: @brand-primary;
}

// 正文
:deep(.history > ul) {
  margin-top: -10px;
  margin-left: -8px;
}

:deep(.history > ul > li > ul) {
  margin-left: -8px;
}

:deep(.history li) {
  font-size: 11px;

  .katex {
    svg {
      width: 20px;
    }
  }

  .katex-html {
    display: none;
  }
}

//  调整pre换行
:deep(pre) {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  font-size: 11px;
}

// mtd
:deep(mtd) {
  display: inline-block;
  // color: red
  // white-space: pre-wrap;
  // white-space:nowrap;
  // color: rgb(102, 196, 14);
}

:deep(.blinking-dot) {
  display: inline-block;
  animation: blink 0.5s infinite;
  font-size: 10px;
}

@keyframes blink {
  0%,
  100% {
    color: @blink-light;
  }

  25% {
    color: @blink-medium;
  }

  50% {
    color: @blink-dark;
  }

  75% {
    color: @blink-medium;
  }
}

.AI-content {
  .textY {
    font-size: @font-size-sm;
    margin-top: 15px;
    font-weight: 600;
  }

  display: flex;
  justify-content: space-between;
  height: 100%;
  box-sizing: border-box;

  .AI-left {
    // flex: 1;
    width: 30%;
    // width: 540px;
    background-color: @background-light;
    // height: 100%;
    // height: 1077px;
    height: 100vh;
    padding: @spacing-xl 15px;
    border-radius: @border-radius-xl;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .Bghead {
      display: flex;
      align-items: center;
      font-family: SourceHanSansCN-Bold;
      font-size: 15px;
      font-weight: 600;
      font-style: italic;
      font-stretch: normal;
      // line-height: 9px;
      // letter-spacing: 0px;
      color: @text-primary;

      img {
        width: 23px;
        height: 23px;
        margin-right: @spacing-sm;
      }
    }

    .item-listBox {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      margin: 10px 0;

      .item-list {
        padding: @spacing-xs @spacing-sm;
        color: @text-tertiary;
        background-color: @bg-primary;
        margin: 0 9px 4px 0;
        border-radius: 5px;
        box-sizing: border-box;
        font-size: @font-size-xs;
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;
      }

      .item-list:hover {
        background-color: @bg-quaternary;
      }

      .active-class {
        background-color: @brand-primary;
        color: @text-inverse;
        background-image: @gradient-primary;
        box-shadow: 0px 3px 7px 1px @brand-shadow;
      }
    }

    .AI-BottomBox {
      height: 50%;

      .AI-Bottom {
        box-sizing: border-box;
        // height: 400px;
        width: 100%;
        height: 90%;

        .inputlis {
          height: 90%;

          // display: flex;
          // flex-direction: column;
          // justify-content: space-between;
          .custom-placeholder {
            margin-top: 10px;
          }

          .checklist {
            height: 120px;
          }

          .custom-placeholder :deep(.el-textarea__inner) {
            border: none;
            box-shadow: none;
          }
        }

        .selectlis {
          .Binput {
            margin: 20px 0;
            position: relative;

            .appdsearch {
              position: absolute;
              top: 6px;
              right: 7px;
              width: 9%;
              height: 29px;
              background-image: @gradient-secondary;
            }
          }
        }
      }

      .Calsub {
        display: flex;
        flex-direction: column;
        align-items: center;

        // margin-top: 40px;
        // margin-top: auto;
        .couIng {
          height: 40px;
        }

        .Calsubmit {
          border: none;
          width: 216px;
          height: 30px;
          font-size: @font-size-sm;
          background-color: @brand-primary;
          color: @text-inverse;
          border-radius: @border-radius-sm;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background-color: @brand-primary-dark;
          }
        }
      }
    }

    .Cdemon {
      display: flex;
      flex-direction: column;
      font-size: 18px;

      .demonstrationx {
        font-weight: 500;
        margin: 10px 0 10px 0;
      }

      // 调整评分大小
      :deep(.el-rate .el-rate__icon) {
        font-size: 40px;
      }
    }
  }

  .AI-right {
    // flex: 1;
    // width: 60%;
    width: 70%;
    box-sizing: border-box;
    background-color: @bg-primary;
    overflow: auto;
    padding: @spacing-md 10px;
    // height: calc(100% -51px);
    // height: 1077px;
    height: 100vh;

    // scroll-behavior: auto; /* 禁用平滑滚动 */
    .content {
      display: flex;
      flex-direction: column;

      // align-items: center;
      .head {
        width: 40px;
        height: 40px;
        // border-radius: 50%;
        margin-right: 10px;
      }

      .description {
        display: flex;
        margin-bottom: 15px;
        flex-direction: row-reverse;

        .text {
          max-width: 85.5%;
          display: flex;
          align-items: center;
          background: @background-hover;
          margin-right: 5px;
          padding: 13px 9px;
          box-sizing: border-box;
          border-radius: @border-radius-lg;
          // line-height: 40px;
          font-size: @font-size-sm;
        }
      }

      .reply {
        display: flex;
        margin-bottom: 30px;

        .reply_right {
          width: 83%;
          background: @background-hover;
          padding: 11px 9px;
          border-radius: 7px;

          .text_ai {
            display: flex;
            align-items: center;
            // justify-content: center;
            // background: #9aa8b6;
            box-sizing: border-box;
            border-radius: @border-radius-lg;

            // line-height: 40px;
            .text_ai_content {
              // width: 83%;
              font-size: @font-size-md;
              margin-bottom: @spacing-xl;
              display: flex;
              flex-direction: column;
              word-break: break-all; //会断词
              width: 100%;

              // line-height: 40px;
              .conent_val {
                margin-bottom: 40px;
              }

              .conent_val:last-child {
                margin-bottom: 0;
              }

              .title {
                display: block;
                color: @brand-primary;
                margin-bottom: 10px;
                font-weight: 600;
              }

              .animeAiloading {
                width: 30px;
                height: 15px;
                object-fit: cover;
              }

              .err_img {
                text-align: center;
                margin: 0 auto;

                font-size: @font-size-sm;

                img {
                  width: 163px;
                  height: 115px;
                  object-fit: cover;
                }

                p {
                  margin: 0;
                }

                .tip {
                  color: @brand-primary;
                  font-weight: 600;
                  cursor: pointer;
                  display: inline-block;
                  margin-left: 5px;
                  margin-bottom: @spacing-xl;
                }
              }
            }

            .text_ai_content:last-child {
              margin-bottom: 0px;
            }

            // .text_ai_content:last-child {
            //   margin-bottom: 0px;
            // }
          }
        }

        .reply_right_err {
          width: 282px;
          height: 174px;
          // justify-content: center;
          padding-top: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .text_bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
        padding-bottom: 10px;
        box-sizing: border-box;

        .botton_left {
          width: 117px;
          height: 35px;
          background-color: @brand-primary;
          box-shadow: 0px 3px 7px 1px @brand-shadow;
          border-radius: @border-radius-sm;
          font-size: @font-size-md;
          font-weight: normal;
          font-stretch: normal;
          line-height: 9px;
          letter-spacing: 0px;
          color: @text-inverse;
          margin-right: 26px;
          cursor: pointer;
        }

        .disabled-btn {
          background-color: @brand-primary-light;
          cursor: not-allowed;
        }

        .botton_right {
          width: 117px;
          height: 35px;
          background-color: @brand-primary-lighter;
          border-radius: @border-radius-sm;
          border: solid 1px @brand-primary;
          font-size: @font-size-md;
          font-weight: normal;
          font-stretch: normal;
          line-height: 9px;
          letter-spacing: 0px;
          color: @brand-primary;
        }
        .isLiked {
          display: flex;
          img {
            width: 19px;
            height: 19px;
            cursor: pointer;
            margin-left: 15px;
          }
        }
      }
    }
  }
}

.animeAiloadingx {
  width: 30px;
  height: 15px;
  object-fit: cover;
  margin-left: 4px;
  margin-bottom: -2px;
  box-sizing: border-box;
}

// 调整 input 内容字体
:deep(.el-textarea__inner) {
  border: none;
  font-size: @font-size-xs;
  position: relative;
}

:deep(::-webkit-scrollbar) {
  width: 3px;
  /* 宽度设为0 */
  height: 0;
  /* 高度设为0 */
  margin-right: 3px;
}

:deep(.el-textarea .el-input__count) {
  // position: absolute;
  // right: 10px;
  // bottom: -30px;
  font-size: 8px;
}

// 调整checkbox字体
:deep(.el-checkbox.el-checkbox--large .el-checkbox__label) {
  font-size: @font-size-xs;
}

// 调整checkbox选框
:deep(.el-checkbox__inner) {
  position: relative;
  border: none;
}

:deep(.el-checkbox__inner:after) {
  position: absolute;
  left: 5px;
  top: 1.5px;
}

.el-divider--horizontal {
  margin: 10px 0 @spacing-xl 0;
  border-top: 1px solid @border-secondary;
}

:deep(svg) {
  width: 20px;
}

.None {
  font-size: 14px;
  white-space: nowrap;
}
:deep(pre) {
  font-size: 9px !important;
  line-height: 1.3 !important;
}

// 回到底部按钮样式
.back-to-bottom {
  position: fixed;
  right: 34%;
  bottom: 10%;
  width: 30px;
  height: 30px;
  border-radius: @border-radius-round;
  background-color: @brand-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 6px @shadow-light;
  cursor: pointer;
  z-index: 5;
  transition: all 0.3s;

  .el-icon {
    font-size: @spacing-xl;
    color: @text-inverse;
  }

  &:hover {
    background-color: @brand-primary-dark;
  }
}

//思考完成按钮样式
// :deep(.trigger) {
//   background-color: @brand-primary-lighter !important;
//   color: @brand-primary !important;
// }
.function_container {
  display: flex;
  flex-direction: column;
  span {
    font-size: @font-size-xs;
  }
}
</style>
<style lang="less">
// 导入主题配色文件
@import "~/styles/theme.less";

.element-bl-pc-sc {
  font-size: @font-size-xs;
  padding: 5px @spacing-sm !important;
}

.el-message__content {
  font-size: @font-size-xs !important;
}

.animeAiloading {
  width: 30px;
  height: 15px;
  object-fit: cover;
}
</style>
