class MyScroller {
  container;
  content;
  scrollEl;
  constructor(el) {
    // if (MyScroller.instance) return MyScroller.instance;
    this.container = el;
    this.init();
    MyScroller.instance = this;
  }

  init() {
    this.container?.style.setProperty('overflow', 'auto');
    this.container?.style.setProperty('overflow-x', 'hidden');
    this.createScrollContent();
  }

  createScrollContent() {
    const child = this.container?.firstChild;
    if (child) {
      this.content = document.createElement('div');
      this.content.classList.add('_scroll-content');
      this.container?.replaceChild(this.content, child);
      this.content?.appendChild(child);
      this._refresh();
    }
  }

  // 刷新
  _refresh() {
    const child = this.content?.firstChild;
    if (child) {
      const rect = child.getBoundingClientRect();
      this.content?.style.setProperty('width', `${rect.width}px`);
      this.content?.style.setProperty('height', `100vh`);
    }
  }

  // static instance: MyScroller;
  static init(el) {
    return new MyScroller(el);
  }

  static refresh() {
    MyScroller.instance._refresh();
  }
}

export default MyScroller;
