/**
 * 接口域名的管理
 * @param {String} apiServer [api服务器]
 * @param {String} otherServer [其他服务器]
 */
const domainLsit = [
  // 开发服
  {
    // apiServer: "http://10.0.30.109:9004/", //qi
    apiServer: "http://10.0.30.183:9004/", //qiu
    csServer: "https://api.ebag-test.readboy.com/wps", //生产环境预览地址 0
    // wsApiServer: "ws://10.0.30.109:18091/chat", //qi
    wsApiServer: "ws://10.0.30.183:18091/chat", //qiu
    webServer: "http://k8s-dev.boran-tech.cn:31467", //门户端
  },
  // 测试环境
  {
    apiServer: "http://k8s-dev.boran-tech.cn:30969", //测试服务 1
    csServer: "https://api.ebag.readboy.com/wps", //测试服务预览地址 1
    wsApiServer: "ws://k8s-dev.boran-tech.cn:31444/chat", //测试服务器
    webServer: "http://k8s-dev.boran-tech.cn:31467", //门户端
  },
  // 正式服
  {
    apiServer: "https://ai-curriculum.gostaredu.com/api-server/", //开发服务 2
    csServer: "https://api.ebag.readboy.com/wps",
    wsApiServer: "wss://ai-curriculum.gostaredu.com/chat",
    webServer: "https://gs-svc.gostaredu.com/p-api", // 门户端
  },
];

// 新窗口地址
const domainLsitadmin = [
  {
    // apiServer: 'http://***********:8081',
    // apiServer: 'http://***********:9527/',
    // apiServer: 'http://***********:9527/',
    apiServer: "http://k8s-dev.boran-tech.cn:32027/",
  },
  {
    apiServer: "https://web.aipreparing.boran-tech.com/", //测试环境
  },
];
// vue
const ServerNumber = import.meta.env.VITE_APP_SERVER_ID
  ? import.meta.env.VITE_APP_SERVER_ID
  : 0;

// 地址对象
export const baseUrl = domainLsit[ServerNumber];
// 新窗口地址
export const baseUrladmin = domainLsitadmin[ServerNumber];
// api接口
export const apiServer = baseUrl;

export default baseUrl;
