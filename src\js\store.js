export default {
  set user(v) {
    if (!v) {
      sessionStorage.removeItem('tsgc2021_user');
      return;
    }
    sessionStorage.setItem('tsgc2021_user', JSON.stringify(v));
  },
  get user() {
    return JSON.parse(sessionStorage.getItem('tsgc2021_user'));
  },
  completeUser(info) {
    const {
      F_name,
      F_town_id,
      F_town_name,
      F_school_id,
      F_school_name,
      F_experiment,
    } = info;
    const { user } = this;
    user.F_name = F_name;
    user.F_town_id = F_town_id;
    user.F_town_name = F_town_name;
    user.F_school_id = F_school_id;
    user.F_school_name = F_school_name;
    user.F_experiment = F_experiment;
    user.F_new = 0;
    this.user = user;
  },
  // 获取所有角色
  get roleNameInZj() {
    let name = '';
    switch (sessionStorage.getItem('auth_role')) {
      case '2': name = '校级管理员'; break;
      case '3': name = '论文审核员'; break;
      // case '3': name = '活动审核员'; break;
      case '4': name = '评分专家'; break;
      case '5': name = '局管理员'; break;
      default: break;
    }
    return name;
  },
  // 获取用户角色
  get userRole() {
    return sessionStorage.getItem('auth_role') || 0;
  },
  set userRole(v) {
    sessionStorage.setItem('auth_role', v);
  },
  // 普通用户
  get isCommonUser() {
    return this.user.F_role === 1;
  },
  // 校级管理员
  get isSchoolAdmin() {
    return this.user.F_role === 2;
  },
  // 活动审核员
  get isActivityAdmin() {
    return this.user.F_role === 3;
  },
  // 评分专家
  get isCityJudge() {
    return this.user.F_role === 4;
  },
  // 市级管理员
  get isAdmin() {
    return this.user.F_role === 5;
  },
  // 镇区级专家
  get isAreaExpert() {
    return this.user.F_role === 3;
  },
  get isReviewAdmin() {
    return this.user.F_role === 6;
  },
};
