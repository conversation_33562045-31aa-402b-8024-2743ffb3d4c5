// 主题配色 - 活力橙红主题
@brand-primary: #ff5e2f; // 主题主色
@brand-primary-dark: #e64a19; // 主题深调
@brand-primary-light: #ff8a65; // 主题浅调
@brand-primary-lighter: #ffebe6; // 主题极浅调

// 功能色
@success-color: #10b981; // 成功色
@warning-color: #f59e0b; // 警告色
@error-color: #ef4444; // 错误色
@info-color: #ff5e2f; // 信息色 - 使用橙红色替代蓝色

// 中性色
@text-primary: #1f2937; // 主要文本色
@text-secondary: #4b5563; // 次要文本色
@text-tertiary: #9ca3af; // 辅助文本色
@text-inverse: #ffffff; // 反色文本

// 背景色
@bg-primary: #ffffff; // 主要背景色
@bg-secondary: #f9fafb; // 次要背景色
@bg-tertiary: #f3f4f6; // 第三背景色
@bg-quaternary: #e5e7eb; // 第四背景色

// 兼容性变量
@primary-blue: #2d91f8;
@primary-blue-light: #5297fc;
@primary-blue-dark: #0074fc;
@background-light: #fff5f2; // 橙红色主题的浅色背景
@background-hover: #ffe8e1; // 橙红色主题的hover背景色
@info-bg: #ffebe6; // 橙红色主题的信息背景色

// 边框色
@border-primary: #e5e7eb; // 主要边框色
@border-secondary: #d1d5db; // 次要边框色

// 阴影色
@shadow-light: rgba(0, 0, 0, 0.1);
@shadow-medium: rgba(0, 0, 0, 0.15);
@brand-shadow: rgba(255, 94, 47, 0.36);

// 动画色
@blink-light: #cccccc;
@blink-medium: #999999;
@blink-dark: #666666;

// 尺寸
@border-radius-sm: 4px;
@border-radius-md: 6px;
@border-radius-lg: 10px;
@border-radius-xl: 15px;
@border-radius-round: 50%;

@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 12px;
@spacing-lg: 16px;
@spacing-xl: 20px;

@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-md: 14px;
@font-size-lg: 16px;
@font-size-xl: 18px;

// 渐变色
@gradient-primary: linear-gradient(86deg, #ff8a65 0%, #ff5e2f 100%);
@gradient-secondary: linear-gradient(86deg, #5297fc 0%, #0074fc 100%);

// 兼容性变量
@primary-blue: #2d91f8;
@primary-blue-light: #5297fc;
@primary-blue-dark: #0074fc;
@background-light: #ffebe6;
@background-hover: #F3F4F6;
@info-bg: #dbeafe;
