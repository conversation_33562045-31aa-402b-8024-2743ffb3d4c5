<template>
  <div style="height: 100%;" >
    <div class=" w-full box-border ">
      <router-view v-if="$route.meta.keepAlive" v-slot="{ Component }">
        <transition name="move" mode="out-in" class="trans">
          <keep-alive>
            <component :is="Component"></component>
          </keep-alive>
        </transition>
      </router-view>

      <router-view v-if="!$route.meta.keepAlive" v-slot="{ Component }">
        <transition>
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </div>
</template>
<script setup></script>
<style lang="less" scoped>
.w-full{
  height: 100%;
}
.trans{
  // height:calc(100vh - 54px);
}
</style>
