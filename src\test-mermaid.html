<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid测试页面</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .mermaid-container {
            margin: 16px 0;
            padding: 16px;
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }
        .mermaid-error {
            color: #ef4444;
            font-size: 12px;
            padding: 16px;
            background: #f3f4f6;
            border-radius: 4px;
            border: 1px solid #d1d5db;
        }
        button {
            background: #ff5e2f;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e64a19;
        }
        pre {
            background: #f3f4f6;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Mermaid流程图渲染测试</h1>
    
    <div class="test-section">
        <h2>测试1：基本流程图</h2>
        <button onclick="testBasicFlowchart()">渲染基本流程图</button>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2：研学课程流程图</h2>
        <button onclick="testCourseFlowchart()">渲染研学课程流程图</button>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3：HTML内容处理</h2>
        <button onclick="testHtmlProcessing()">测试HTML处理</button>
        <div id="test3-result"></div>
    </div>

    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs';
        
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            themeVariables: {
                primaryColor: '#ff5e2f',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#e5e7eb',
                lineColor: '#9ca3af',
                secondaryColor: '#f9fafb',
                tertiaryColor: '#f3f4f6',
                background: '#ffffff',
                mainBkg: '#ffffff',
                secondBkg: '#f9fafb',
                tertiaryBkg: '#f3f4f6'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            fontFamily: 'system-ui, -apple-system, sans-serif',
            fontSize: 12,
            logLevel: 'error'
        });

        // 渲染Mermaid图表的通用函数
        async function renderMermaid(code, containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '<div>正在渲染...</div>';
            
            try {
                // 生成唯一ID
                const id = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                
                // 验证语法
                const isValid = await mermaid.parse(code);
                if (!isValid) {
                    throw new Error('Invalid mermaid syntax');
                }
                
                // 渲染图表
                const { svg } = await mermaid.render(id, code);
                
                // 创建容器并插入SVG
                container.innerHTML = `
                    <div class="mermaid-container">
                        ${svg}
                    </div>
                    <pre>${code}</pre>
                `;
                
                // 优化SVG样式
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                    svgElement.style.maxWidth = '100%';
                    svgElement.style.height = 'auto';
                    svgElement.style.display = 'block';
                    svgElement.style.margin = '0 auto';
                }
                
                console.log('Mermaid渲染成功:', id);
                
            } catch (error) {
                console.error('Mermaid渲染失败:', error);
                container.innerHTML = `
                    <div class="mermaid-error">
                        <strong>渲染失败:</strong> ${error.message}
                        <pre>${code}</pre>
                    </div>
                `;
            }
        }

        // 测试函数
        window.testBasicFlowchart = () => {
            const code = `graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作A]
    B -->|否| D[执行操作B]
    C --> E[结束]
    D --> E`;
            
            renderMermaid(code, 'test1-result');
        };

        window.testCourseFlowchart = () => {
            const code = `graph TD 
    A[课前准备] --> B[抗战纪念亭研学]
    A --> C[粮食馆改革史探究]
    A --> D[里溪风貌观察]
    B --> E(作业一： 英雄事迹时间轴)
    C --> F(作业二： 改革思维导图)
    D --> G(作业三： 振兴对比图集)
    E --> H[红色精神提炼]
    F --> I[劳动价值分析]
    G --> J[发展启示总结]
    H & I & J --> K[实践手册制作]`;
            
            renderMermaid(code, 'test2-result');
        };

        window.testHtmlProcessing = () => {
            const htmlContent = `
                <h3>这是一个包含Mermaid图表的HTML内容</h3>
                <p>下面是一个流程图：</p>
                <div class="mermaid-wrapper" data-mermaid-code="${encodeURIComponent(`graph LR
    A[输入] --> B[处理]
    B --> C[输出]`)}" data-is-streaming="false"></div>
                <p>图表渲染完成。</p>
            `;
            
            const container = document.getElementById('test3-result');
            container.innerHTML = htmlContent;
            
            // 手动处理Mermaid占位符
            const wrapper = container.querySelector('.mermaid-wrapper');
            if (wrapper) {
                const encodedCode = wrapper.getAttribute('data-mermaid-code');
                if (encodedCode) {
                    const code = decodeURIComponent(encodedCode);
                    const tempId = 'temp-' + Date.now();
                    wrapper.id = tempId;
                    renderMermaid(code, tempId);
                }
            }
        };

        console.log('Mermaid测试页面已加载');
    </script>
</body>
</html>
