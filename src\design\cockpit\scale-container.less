// 缩放的元素样式
// .scale-container {
//   // position: fixed;
//   // top: 50%;
//   // left: 50%;
//   width: 1920px;
//   height: 1080px;
//   // height: 100vh;
//   transform-origin: left top;
// }

// .scroll-container {
//   // 滚动条大小
//   &::-webkit-scrollbar {
//     // 如不要，设置为0
//     width: 0;
//     height: 0;
//   }

//   // 滑块颜色
//   &::-webkit-scrollbar-track {
//     // background-color: red;
//     background-color: transparent;
//   }

//   // 轨迹颜色
//   &::-webkit-scrollbar-thumb {
//     background-color: transparent;
//   }
// }