import md5 from "js-md5";
import _ from "lodash";
import { v4 as uuidv4 } from "uuid";
import { sortObj } from "@iceywu/utils";
const secret_key = {
  apiServer: "II1HOIrUHgxsgisFaeoncKS0ratDXT5N",
  webServer: "NXW2xapdvjhYaVkPeK6wzy6Cykx3yzQG",
};

let getSign = (data = {}, uuid, timestamp, serverName) => {
  for (let key in data) {
    if (Array.isArray(data[key])) {
      data[key] = JSON.stringify(data[key]);
    }
  }

  let s = "";
  if (data["nonce"] === null || data["nonce"] === undefined) {
    data["nonce"] = uuid;
  }
  if (data["timestamp"] === null || data["timestamp"] === undefined) {
    data["timestamp"] = timestamp;
  }
  // data["nonce"] ??= uuid;
  // data["timestamp"] ??= timestamp;
  let keys = Object.keys(data);
  keys.sort();
  data["key"] = secret_key[serverName || "apiServer"];
  keys.push("key");
  for (let i = 0; i < keys.length; i++) {
    s += `${i == 0 ? "" : "&"}${keys[i]}=${data[keys[i]]}`; // if(keys[i] === 'sort' && config.url === '/back/roleCategory/findAll'){ //   s+= '&sort=createAt,desc' // }
  } // s+= '&sort=createAt,desc&key=jbNiiC3tujoK5fAAcHHnbLp8pRe6j1vR'
  // console.log("s", s);
  // console.log('🍭-----s-----', s);
  return md5(s);
};

export const encrypt = (data = {}, serverName = "apiServer") => {
  let tempData = {};
  const uuid = uuidv4();
  const timestamp = new Date().getTime();
  const baseData = sortObj({
    ...data,
  });
  let sign = getSign(baseData, uuid, timestamp, serverName);
  // console.log('🎉-----sign-----', sign);
  return {
    nonce: uuid,
    timestamp,
    sign,
    tempData: data,
  };
};
