<script setup>
import { useTitle, useFavicon, useLocalStorage } from "@vueuse/core";
import { getPlatformConfig } from "../src/api/admin";
import { ImageThumbnail } from "../src/utils/imageProxy";

const platformInfo = useLocalStorage("platformInfo", {
      platformName: "",
      platformIcon: "",
      platformLogo: "",
      platformLogoLight: "",
      platformLogoVertical: "",
      platformSlogan: ""
    });

    // 获取平台信息
    const getPlatformInfo = async () => {
      try {
        const res = await getPlatformConfig();
        // 处理图片URL，存入localStorage前先进行代理
        const processedData = {
          ...res.data,
          platformIcon: ImageThumbnail(res.data?.platformIcon, "32x")
        };
        // 更新 localStorage 中的数据
        platformInfo.value = processedData;
      } catch (error) {
        console.error("获取平台配置失败:", error);
      }
    };

    // 立即执行获取平台信息
    getPlatformInfo();

    // 监听平台信息变化，更新标题和图标
    watchEffect(() => {
      if (platformInfo.value) {
        // 设置网页标题
        // useTitle(platformInfo.value.platformName || "平台运营端");
        // 设置网页图标
        useFavicon(
          platformInfo.value.platformIcon ||
          "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
        );
      }
    });
</script>

<template>
  <el-config-provider  namespace="el" size="small">
    <router-view />
  </el-config-provider>
</template>

<style lang="less">
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #f5f5f5;
}
::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}
::-webkit-scrollbar-track {
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
