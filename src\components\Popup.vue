<template>
  <div>
    <el-dialog class="dialog" :model-value="props.centerDialogVisible" :show-close="false" :close-on-click-modal="false" align-center>
      <!-- <span>Open the dialog from the center from the screen</span> -->
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button @click="open">Cancel</el-button>
        </div>
      </template> -->
      <div class="content">
        <div style="position: relative;">
          <img class="right" :src="typeSet(props?.setPopu)" alt="">
          <!-- <img class="left" :src="switchImg()" alt=""> -->
        </div>

      </div>
      <div class="txt">{{ textType(props?.setPopu) }}</div>
      <!-- {{ props.condition }} -->
    </el-dialog>
  </div>
</template>

<script setup>
import iconJiangyi from "../assets/anime_jiangyi.gif";
import iconNoJiangyi from "../assets/anime_jiangyi_failure.gif";
import iconJiaoan from "../assets/anime_jiaoan.gif";
import iconNoJiaoan from "../assets/anime_jiaoan_failure.gif";
import iconKejian from "../assets/anime_kejian.gif";
import iconNoKejian from "../assets/anime_kejian_failure.gif";

import animeLoading from "../assets/anime_loading.gif";

import iconShijuan from "../assets/anime_shijuan.gif";
import iconNoShijuan from "../assets/anime_shijuan_failure.gif";
import tupu from "../assets/icon_tupu.png";
import ziyuanku from "../assets/icon_ziyuanku.png";
// import animeLoading from "../assets/anime-loading.gif";
import failure from "../assets/failure.png";
import success from "../assets/success.png";
import { onMounted } from "vue";

const props = defineProps({
  // data: {
  //   type: Object,
  //   required: true,
  // },
  centerDialogVisible: {
    type: Boolean,
  },
  setPopu: {
    type: String,
    required: 'COURSEWARE',
  },
  updatedAtTime: {
    type: Boolean,
  },
  condition: {
    type: Boolean,
  }
});
const emit = defineEmits(["setCenterDialogVisible"]);
const open = () => {
  emit("setCenterDialogVisible")
}
// const centerDialogVisible = ref(false)
const typeSet = (val) => {
  const imgType = {
    'INSTRUCTIONAL_DESIGN':iconKejian,
    "COURSEWARE": iconKejian,
    "TEACHING_PLAN": iconJiaoan,
    "HANDOUTS": iconJiangyi,
    "TALK": iconJiangyi,
    "EXAMINATION": iconShijuan,
  }
  const imgType2 = {
    'INSTRUCTIONAL_DESIGN':iconNoKejian,
    "COURSEWARE": iconNoKejian,
    "TEACHING_PLAN": iconNoJiaoan,
    "HANDOUTS": iconNoJiangyi,
    "TALK": iconNoKejian,
    "EXAMINATION": iconNoShijuan,
  }
  // console.log('🦄-----props.setPopu-----', val);
  console.log('🐬-----imgType[val]-----', imgType[val]);
  if (props.updatedAtTime) {
    return animeLoading
  } else if (props.updatedAtTime == false && props.condition) {
    return imgType[val]
  } else if (props.condition == false) {
    return imgType2[val]
  }
  // return imgType[val]
}
const textType = (val) => {
  const txtType = {
    'INSTRUCTIONAL_DESIGN':'文档',
    "COURSEWARE": '课件',
    "TEACHING_PLAN": '教案',
    "HANDOUTS": '讲义',
    "TALK": '文档',
    "EXAMINATION": '试卷',
  }
  let copywriting = '';
  // if (val == 'TALK' && props.updatedAtTime) {
  //   copywriting = `正在${txtType[val]}...`
  // } else if (val == 'TALK' && props.condition == false) {
  //   copywriting = `${txtType[val]}生成失败`
  // } else {
  //   copywriting = `${txtType[val]}已生成`
  // }
  // if (val != 'TALK' && props.updatedAtTime) {
  //   copywriting = `正在生成${txtType[val]}...`
  // } else if(props.updatedAtTime == false && props.condition) {
  //   copywriting = `${txtType[val]}已生成~`
  // } else if(props.updatedAtTime == false && props.condition == false) {
  //   copywriting = `${txtType[val]}生成失败`
  // }

  if (val === 'TALK') {
    if (props.updatedAtTime) {
      copywriting = `正在生成${txtType[val]}...`;
    } else {
      copywriting = props.condition ? `${txtType[val]}已生成` : `${txtType[val]}生成失败`;
    }
  } else {
    if (props.updatedAtTime) {
      copywriting = `正在生成${txtType[val]}...`;
    } else {
      copywriting = props.condition ? `${txtType[val]}已生成~` : `${txtType[val]}生成失败`;
    }
  }
  return copywriting
}
const switchImg = () => {
  console.log('🎉-----props.updatedAtTime-----', props.updatedAtTime, props.condition);
  if (props.updatedAtTime) {
    return animeLoading
  } else if (props.updatedAtTime == false && props.condition) {
    return success
  } else if (props.condition == false) {
    return failure
  }
}
onMounted(() => {
})
</script>

<style lang="less" scoped>
:deep(.el-dialog__header){
display: none;
}
:deep(.el-dialog) {
  width: 252px;
  height: 165px;
  // background-image: linear-gradient(0deg,
  // 	#ffffff 0%,
  // 	#e2f2ff 100%);
  border-radius: 12px;
  background: url(../assets/bg.png) no-repeat;
  background-size: cover;
}
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  .right {
    // margin-top: 45px;
    // margin-top:8px;
    width:142px;
    height: 92px;
  }
  .left {
    position: absolute;
    bottom: 17px;
    right: 27px;
    width: 29px;
    height: 29px;
  }
}

.txt {
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  // line-height: 9px;
  letter-spacing: 0px;
  color: #222222;
  text-align: center;
}
</style>