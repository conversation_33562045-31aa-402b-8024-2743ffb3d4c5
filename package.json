{"name": "research-learning-curriculum-design-web", "private": true, "version": "1.5.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:dev": "vite build --mode development", "build:brdev": "vite build --mode brdev", "preview": "vite preview", "version": "standard-version --tag-prefix \"v\"", "versionMajor": "standard-version --tag-prefix \"v\" --release-as major"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iceywu/utils": "^0.0.49", "@vueuse/core": "^10.1.2", "@vueuse/integrations": "^10.1.2", "@vueuse/motion": "2.0.0", "axios": "^1.4.0", "element-plus": "^2.10.2", "gm-crypto": "^0.1.12", "js-cookie": "^3.0.5", "js-md5": "^0.7.3", "katex": "^0.16.11", "less": "^4.1.3", "less-loader": "^11.1.3", "markdown-it": "^14.1.0", "marked": "^14.1.3", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^3.1.0", "spark-md5": "^3.0.2", "uuid": "^10.0.0", "vue": "^3.3.4", "vue-element-plus-x": "^1.2.0", "vue-router": "^4.2.2"}, "devDependencies": {"@iconify-json/carbon": "^1.1.17", "@unocss/reset": "^0.53.1", "@vitejs/plugin-legacy": "^4.0.4", "@vitejs/plugin-vue": "^4.2.3", "postcss": "^8.4.24", "postcss-px-to-viewport": "^1.1.1", "terser": "^5.17.7", "unocss": "^0.53.1", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "unplugin-vue-macros": "^2.3.0", "vite": "^4.3.9", "vite-plugin-mock": "^3.0.0", "vite-plugin-pages": "^0.31.0", "vite-plugin-vue-inspector": "^5.3.1"}}