<template>
  <div ref="webofficeElref" class="weboffice__wrapper" :style="{ height: props.height }"></div>
</template>

<script setup>
import { ref, watch, defineProps } from "vue";
import { officeUrl } from "../api/index";
import WebOfficeSDK from "../js/web-office/web-office-sdk-solution-v1.1.27.es";

const webofficeElref = ref(null);
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  url: {
    type: String,

  },
  fileName: {
    type: String,
  },
  height: {
    type: [String],
    default: "500px",
  },
});

// 处理文件类型并生成预览所需的参数
const prepareFilePreviewParams = (file) => {
  // const parts = file.url.split('/');
  // const fileNameWithExtension = parts[parts.length - 1];
  // const fileName = fileNameWithExtension; // 保留完整的文件名

  console.log('🍭-----file.url-----', file);
  const ext = file.url.split(".").pop();
  const fileId = file.url.split("/").pop().split(".")[0];
  
  return {
    F_accesstoken: "CFIsGgvkonYEoVURomTZCk6HwshEQhRw",
    F_file_id: fileId,
    F_student_id: "12345",
    F_file_url: file.url,
    F_file_name: file.fileName, // 文件名已包含扩展名
    F_file_ext: `.${ext}`,
    F_share: 4,
  };
};

// 获取文件预览地址并初始化预览组件
const fetchAndInitPreview = async (file) => {
  console.log('🍪-----file.url-----', file);
  const params = prepareFilePreviewParams(file);
  console.log('🐬-----params-----', params);
  const res = await officeUrl(params);
  if (res && res.F_responseNo === 10000) {
    console.log('🐳-----res-----', res);
    const match = res.F_url.split('/').pop().split('?')[0];
    const id = match;
      // const dotIndex = file.url.lastIndexOf('.');
    // console.log('🍪-----file.url.split(".").pop()-----',id, file.url.split(".").pop());
    initPreview(id, file.url.split(".").pop());
  } else {
    // 这里可以添加错误处理逻辑，比如显示错误信息给用户
  }
};
// 初始化预览组件
const initPreview = (id, ext) => {
  const fileTypeMapping = {
    pdf: "f",
    doc: "w",
    docx: "w",
    wps: "w",
    pptx: "p",
    ppt: "p",
    xls: "s",
    xlsx: "s",
    dbt: "d",
    // 其他文件类型...
  };
  const officeType = fileTypeMapping[ext.toLowerCase()] || "o";
  WebOfficeSDK.init({
    officeType:officeType,
    appId: "AK20230628COKMWP",
    fileId: id,
    mount: webofficeElref.value,
    mode: "simple",
  });
};

// 监听props.data的变化
watch(
  () => props.url,
  (newVal) => {
    if (newVal) {
      console.log("🎉-----newVal-----", newVal);
      let objurl = {
        url: newVal,
        fileName: props.fileName,
      }

      fetchAndInitPreview(objurl);
    }
  },
  { immediate: true }
);
</script>

<style lang="less" scoped></style>
