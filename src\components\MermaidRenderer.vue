<template>
  <div class="mermaid-container">
    <div 
      ref="mermaidRef" 
      class="mermaid-diagram"
      :class="{ 'mermaid-error': hasError }"
    >
      <div v-if="isLoading" class="mermaid-loading">
        <span>正在渲染图表...</span>
      </div>
      <div v-else-if="hasError" class="mermaid-error-message">
        <span>图表渲染失败，显示原始代码：</span>
        <pre>{{ mermaidCode }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import mermaid from 'mermaid'

const props = defineProps({
  code: {
    type: String,
    required: true
  },
  // 用于标识是否为流式输出中的内容
  isStreaming: {
    type: Boolean,
    default: false
  }
})

const mermaidRef = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const mermaidCode = ref('')

// 初始化Mermaid配置
const initMermaid = () => {
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    themeVariables: {
      primaryColor: '#ff5e2f',
      primaryTextColor: '#1f2937',
      primaryBorderColor: '#e5e7eb',
      lineColor: '#9ca3af',
      secondaryColor: '#f9fafb',
      tertiaryColor: '#f3f4f6',
      background: '#ffffff',
      mainBkg: '#ffffff',
      secondBkg: '#f9fafb',
      tertiaryBkg: '#f3f4f6'
    },
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: 'basis'
    },
    fontFamily: 'system-ui, -apple-system, sans-serif',
    fontSize: 12,
    logLevel: 'error'
  })
}

// 检查Mermaid代码是否完整
const isCodeComplete = (code) => {
  if (!code || typeof code !== 'string') return false
  
  const trimmedCode = code.trim()
  if (!trimmedCode) return false
  
  // 检查是否以graph开头
  if (!trimmedCode.match(/^(graph|flowchart|sequenceDiagram|classDiagram|stateDiagram|erDiagram|journey|gantt|pie|gitgraph)/)) {
    return false
  }
  
  // 对于graph类型，检查是否有基本的节点定义
  if (trimmedCode.startsWith('graph') || trimmedCode.startsWith('flowchart')) {
    // 简单检查是否包含箭头或节点定义
    return trimmedCode.includes('-->') || trimmedCode.includes('---') || trimmedCode.includes('[') || trimmedCode.includes('(')
  }
  
  return true
}

// 渲染Mermaid图表
const renderMermaid = async () => {
  if (!mermaidRef.value || !props.code) {
    console.log('MermaidRenderer: No ref or code', { ref: !!mermaidRef.value, code: props.code })
    return
  }

  const code = props.code.trim()
  mermaidCode.value = code

  console.log('MermaidRenderer: Attempting to render', { code, isStreaming: props.isStreaming })

  // 如果是流式输出且代码不完整，不进行渲染
  if (props.isStreaming && !isCodeComplete(code)) {
    console.log('MermaidRenderer: Code incomplete, skipping render')
    return
  }

  isLoading.value = true
  hasError.value = false

  try {
    // 清空容器
    mermaidRef.value.innerHTML = ''

    // 生成唯一ID
    const id = `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    console.log('MermaidRenderer: Parsing code...')

    // 验证语法
    const isValid = await mermaid.parse(code)
    if (!isValid) {
      throw new Error('Invalid mermaid syntax')
    }

    console.log('MermaidRenderer: Code valid, rendering...')

    // 渲染图表
    const { svg } = await mermaid.render(id, code)

    // 插入SVG
    mermaidRef.value.innerHTML = svg

    // 添加样式优化
    const svgElement = mermaidRef.value.querySelector('svg')
    if (svgElement) {
      svgElement.style.maxWidth = '100%'
      svgElement.style.height = 'auto'
      svgElement.style.display = 'block'
      svgElement.style.margin = '0 auto'
    }

    console.log('MermaidRenderer: Render successful')

  } catch (error) {
    console.warn('Mermaid rendering failed:', error)
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

// 监听代码变化
watch(() => props.code, async (newCode, oldCode) => {
  if (newCode !== oldCode && newCode) {
    await nextTick()
    await renderMermaid()
  }
}, { immediate: false })

// 组件挂载时初始化
onMounted(async () => {
  initMermaid()
  if (props.code) {
    await nextTick()
    await renderMermaid()
  }
})

// 暴露重新渲染方法
defineExpose({
  rerender: renderMermaid
})
</script>

<style lang="less" scoped>
@import "~/styles/theme.less";

.mermaid-container {
  margin: @spacing-md 0;
  padding: @spacing-md;
  background: @bg-primary;
  border: 1px solid @border-primary;
  border-radius: @border-radius-md;
  overflow: hidden;
}

.mermaid-diagram {
  text-align: center;
  
  &.mermaid-error {
    text-align: left;
  }
}

.mermaid-loading {
  padding: @spacing-lg;
  color: @text-tertiary;
  font-size: @font-size-sm;
  text-align: center;
}

.mermaid-error-message {
  color: @error-color;
  font-size: @font-size-sm;
  
  span {
    display: block;
    margin-bottom: @spacing-sm;
    font-weight: 500;
  }
  
  pre {
    background: @bg-tertiary;
    padding: @spacing-md;
    border-radius: @border-radius-sm;
    font-family: 'Courier New', monospace;
    font-size: @font-size-xs;
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0;
    color: @text-primary;
  }
}

// Mermaid图表样式优化
:deep(.mermaid) {
  svg {
    max-width: 100% !important;
    height: auto !important;
  }
  
  // 节点样式
  .node rect,
  .node circle,
  .node ellipse,
  .node polygon {
    fill: @bg-primary;
    stroke: @brand-primary;
    stroke-width: 2px;
  }
  
  // 文本样式
  .node text,
  .edgeLabel text {
    fill: @text-primary;
    font-family: system-ui, -apple-system, sans-serif;
    font-size: 12px;
  }
  
  // 连接线样式
  .edgePath path {
    stroke: @brand-primary;
    stroke-width: 2px;
  }
  
  // 箭头样式
  .arrowheadPath {
    fill: @brand-primary;
    stroke: @brand-primary;
  }
}
</style>
