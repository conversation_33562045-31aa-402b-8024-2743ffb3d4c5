<template>
  <div class="mermaid-container">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="mermaid-loading">
      <div class="loading-spinner"></div>
      <span>正在渲染图表...</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="hasError" class="mermaid-error">
      <div class="error-icon">⚠️</div>
      <div class="error-message">
        <p>图表渲染失败</p>
        <p class="error-detail">{{ errorMessage }}</p>
        <button @click="retryRender" class="retry-btn">重试</button>
      </div>
    </div>
    
    <!-- 图表容器 -->
    <div 
      v-else 
      ref="mermaidContainer" 
      class="mermaid-chart"
      :class="{ 'chart-rendered': isRendered }"
    ></div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue'
import mermaid from 'mermaid'

const props = defineProps({
  // mermaid 图表代码
  code: {
    type: String,
    required: true
  },
  // 是否为流式数据（用于判断是否需要等待完整数据）
  isStreaming: {
    type: Boolean,
    default: false
  },
  // 主题配置
  theme: {
    type: String,
    default: 'default'
  },
  // 自定义配置
  config: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['rendered', 'error'])

// 响应式数据
const mermaidContainer = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const isRendered = ref(false)
const renderTimer = ref(null)

// mermaid 初始化配置
const defaultConfig = {
  startOnLoad: false,
  theme: props.theme,
  securityLevel: 'loose',
  fontFamily: 'Arial, sans-serif',
  fontSize: 14,
  flowchart: {
    useMaxWidth: true,
    htmlLabels: true,
    curve: 'basis'
  },
  sequence: {
    useMaxWidth: true,
    wrap: true
  },
  gantt: {
    useMaxWidth: true
  },
  journey: {
    useMaxWidth: true
  },
  timeline: {
    useMaxWidth: true
  }
}

// 检查 mermaid 代码是否完整
const isCodeComplete = (code) => {
  if (!code || typeof code !== 'string') return false
  
  const trimmedCode = code.trim()
  if (!trimmedCode) return false
  
  // 检查是否有基本的 mermaid 语法
  const mermaidPatterns = [
    /^graph\s+(TD|TB|BT|RL|LR)/i,
    /^flowchart\s+(TD|TB|BT|RL|LR)/i,
    /^sequenceDiagram/i,
    /^classDiagram/i,
    /^stateDiagram/i,
    /^erDiagram/i,
    /^journey/i,
    /^gantt/i,
    /^pie/i,
    /^gitgraph/i,
    /^mindmap/i,
    /^timeline/i
  ]
  
  const hasValidStart = mermaidPatterns.some(pattern => pattern.test(trimmedCode))
  if (!hasValidStart) return false
  
  // 对于流程图，检查是否有基本的节点定义
  if (/^(graph|flowchart)/i.test(trimmedCode)) {
    // 简单检查是否包含节点定义（至少一个箭头或节点）
    return /-->|---|\[|\(|\{/.test(trimmedCode)
  }
  
  return true
}

// 渲染 mermaid 图表
const renderMermaid = async () => {
  if (!mermaidContainer.value || !props.code) return
  
  // 如果是流式数据且代码不完整，则不渲染
  if (props.isStreaming && !isCodeComplete(props.code)) {
    return
  }
  
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''
  
  try {
    // 清空容器
    mermaidContainer.value.innerHTML = ''
    
    // 合并配置
    const config = { ...defaultConfig, ...props.config }
    mermaid.initialize(config)
    
    // 生成唯一ID
    const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    // 渲染图表
    const { svg } = await mermaid.render(id, props.code)
    
    // 插入SVG
    mermaidContainer.value.innerHTML = svg
    
    isRendered.value = true
    isLoading.value = false
    
    emit('rendered', { svg, container: mermaidContainer.value })
    
  } catch (error) {
    console.error('Mermaid render error:', error)
    hasError.value = true
    isLoading.value = false
    errorMessage.value = error.message || '未知错误'
    emit('error', error)
  }
}

// 重试渲染
const retryRender = () => {
  hasError.value = false
  renderMermaid()
}

// 防抖渲染函数
const debouncedRender = () => {
  if (renderTimer.value) {
    clearTimeout(renderTimer.value)
  }
  
  renderTimer.value = setTimeout(() => {
    renderMermaid()
  }, props.isStreaming ? 500 : 100) // 流式数据延迟更长
}

// 监听代码变化
watch(
  () => props.code,
  (newCode) => {
    if (newCode) {
      debouncedRender()
    }
  },
  { immediate: true }
)

// 监听主题变化
watch(
  () => props.theme,
  () => {
    if (props.code) {
      renderMermaid()
    }
  }
)

// 组件挂载时初始化
onMounted(() => {
  if (props.code) {
    nextTick(() => {
      renderMermaid()
    })
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (renderTimer.value) {
    clearTimeout(renderTimer.value)
  }
})
</script>

<style lang="less" scoped>
.mermaid-container {
  width: 100%;
  margin: 16px 0;
  
  .mermaid-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10px;
    }
  }
  
  .mermaid-error {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    color: #f56c6c;
    
    .error-icon {
      font-size: 24px;
      margin-right: 12px;
    }
    
    .error-message {
      flex: 1;
      
      p {
        margin: 0 0 4px 0;
        
        &.error-detail {
          font-size: 12px;
          color: #999;
        }
      }
      
      .retry-btn {
        margin-top: 8px;
        padding: 4px 12px;
        background-color: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        
        &:hover {
          background-color: #66b1ff;
        }
      }
    }
  }
  
  .mermaid-chart {
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    
    &.chart-rendered {
      opacity: 1;
    }
    
    :deep(svg) {
      max-width: 100%;
      height: auto;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
