/**
 * Mermaid工具函数
 * 用于检测、提取和处理Mermaid代码块
 */

/**
 * 检测文本中是否包含Mermaid代码块
 * @param {string} text - 要检测的文本
 * @returns {boolean} 是否包含Mermaid代码块
 */
export function hasMermaidCode(text) {
  if (!text || typeof text !== 'string') return false
  
  // 检测```mermaid代码块
  const mermaidBlockRegex = /```mermaid\s*\n([\s\S]*?)```/gi
  return mermaidBlockRegex.test(text)
}

/**
 * 提取文本中的所有Mermaid代码块
 * @param {string} text - 包含Mermaid代码的文本
 * @returns {Array} Mermaid代码块数组，每个元素包含{code, startIndex, endIndex}
 */
export function extractMermaidBlocks(text) {
  if (!text || typeof text !== 'string') return []
  
  const blocks = []
  const mermaidBlockRegex = /```mermaid\s*\n([\s\S]*?)```/gi
  let match
  
  while ((match = mermaidBlockRegex.exec(text)) !== null) {
    blocks.push({
      code: match[1].trim(),
      startIndex: match.index,
      endIndex: match.index + match[0].length,
      fullMatch: match[0]
    })
  }
  
  return blocks
}

/**
 * 检查Mermaid代码是否完整（用于流式输出）
 * @param {string} code - Mermaid代码
 * @returns {boolean} 代码是否完整
 */
export function isMermaidCodeComplete(code) {
  if (!code || typeof code !== 'string') return false
  
  const trimmedCode = code.trim()
  if (!trimmedCode) return false
  
  // 检查是否以支持的图表类型开头
  const supportedTypes = [
    'graph', 'flowchart', 'sequenceDiagram', 'classDiagram', 
    'stateDiagram', 'erDiagram', 'journey', 'gantt', 'pie', 'gitgraph'
  ]
  
  const startsWithSupportedType = supportedTypes.some(type => 
    trimmedCode.toLowerCase().startsWith(type.toLowerCase())
  )
  
  if (!startsWithSupportedType) return false
  
  // 对于graph和flowchart类型，检查是否有基本的节点或连接定义
  if (trimmedCode.toLowerCase().startsWith('graph') || trimmedCode.toLowerCase().startsWith('flowchart')) {
    // 检查是否包含箭头、节点定义或连接
    const hasConnections = /-->|---|==>/g.test(trimmedCode)
    const hasNodes = /\[.*?\]|\(.*?\)|\{.*?\}|>.*?]|\[\[.*?\]\]/g.test(trimmedCode)
    
    return hasConnections || hasNodes
  }
  
  return true
}

/**
 * 检测流式文本中的不完整Mermaid代码块
 * @param {string} text - 流式文本
 * @returns {Object|null} 不完整的代码块信息或null
 */
export function detectIncompleteMermaidBlock(text) {
  if (!text || typeof text !== 'string') return null
  
  // 查找未闭合的```mermaid代码块
  const incompleteBlockRegex = /```mermaid\s*\n([\s\S]*?)$/i
  const match = incompleteBlockRegex.exec(text)
  
  if (match) {
    const code = match[1].trim()
    return {
      code,
      startIndex: match.index,
      isComplete: false
    }
  }
  
  return null
}

/**
 * 替换文本中的Mermaid代码块为占位符
 * @param {string} text - 原始文本
 * @param {string} placeholder - 占位符模板，{index}会被替换为索引
 * @returns {Object} {text: 处理后的文本, blocks: 提取的代码块数组}
 */
export function replaceMermaidBlocks(text, placeholder = '[MERMAID_BLOCK_{index}]') {
  if (!text || typeof text !== 'string') return { text, blocks: [] }
  
  const blocks = extractMermaidBlocks(text)
  let processedText = text
  
  // 从后往前替换，避免索引偏移问题
  for (let i = blocks.length - 1; i >= 0; i--) {
    const block = blocks[i]
    const placeholderText = placeholder.replace('{index}', i)
    processedText = processedText.substring(0, block.startIndex) + 
                   placeholderText + 
                   processedText.substring(block.endIndex)
  }
  
  return {
    text: processedText,
    blocks
  }
}

/**
 * 恢复文本中的Mermaid代码块占位符
 * @param {string} text - 包含占位符的文本
 * @param {Array} blocks - 代码块数组
 * @param {string} placeholder - 占位符模板
 * @returns {string} 恢复后的文本
 */
export function restoreMermaidBlocks(text, blocks, placeholder = '[MERMAID_BLOCK_{index}]') {
  if (!text || !blocks || !Array.isArray(blocks)) return text
  
  let restoredText = text
  
  blocks.forEach((block, index) => {
    const placeholderText = placeholder.replace('{index}', index)
    restoredText = restoredText.replace(placeholderText, block.fullMatch)
  })
  
  return restoredText
}

/**
 * 为流式输出优化的Mermaid检测器
 * 用于判断何时应该触发图表渲染
 */
export class StreamingMermaidDetector {
  constructor() {
    this.buffer = ''
    this.detectedBlocks = []
    this.lastRenderTime = 0
    this.renderDelay = 1000 // 1秒的渲染延迟
  }
  
  /**
   * 添加新的文本片段
   * @param {string} chunk - 新的文本片段
   * @returns {Object} 检测结果
   */
  addChunk(chunk) {
    if (!chunk) return { shouldRender: false, blocks: [] }
    
    this.buffer += chunk
    const currentTime = Date.now()
    
    // 检测完整的代码块
    const completeBlocks = extractMermaidBlocks(this.buffer)
    
    // 检测不完整的代码块
    const incompleteBlock = detectIncompleteMermaidBlock(this.buffer)
    
    // 判断是否应该渲染
    const shouldRender = this.shouldTriggerRender(completeBlocks, incompleteBlock, currentTime)
    
    if (shouldRender) {
      this.lastRenderTime = currentTime
      this.detectedBlocks = [...completeBlocks]
    }
    
    return {
      shouldRender,
      blocks: completeBlocks,
      incompleteBlock,
      buffer: this.buffer
    }
  }
  
  /**
   * 判断是否应该触发渲染
   * @private
   */
  shouldTriggerRender(completeBlocks, incompleteBlock, currentTime) {
    // 如果有新的完整代码块，立即渲染
    if (completeBlocks.length > this.detectedBlocks.length) {
      return true
    }
    
    // 如果有不完整的代码块且距离上次渲染超过延迟时间，尝试渲染
    if (incompleteBlock && 
        incompleteBlock.code && 
        isMermaidCodeComplete(incompleteBlock.code) &&
        currentTime - this.lastRenderTime > this.renderDelay) {
      return true
    }
    
    return false
  }
  
  /**
   * 重置检测器
   */
  reset() {
    this.buffer = ''
    this.detectedBlocks = []
    this.lastRenderTime = 0
  }
  
  /**
   * 获取当前缓冲区内容
   */
  getBuffer() {
    return this.buffer
  }
}

/**
 * 验证Mermaid代码语法（简单验证）
 * @param {string} code - Mermaid代码
 * @returns {boolean} 语法是否有效
 */
export function validateMermaidSyntax(code) {
  if (!code || typeof code !== 'string') return false
  
  const trimmedCode = code.trim()
  if (!trimmedCode) return false
  
  try {
    // 基本的语法检查
    const lines = trimmedCode.split('\n').map(line => line.trim()).filter(line => line)
    
    if (lines.length === 0) return false
    
    // 检查第一行是否是有效的图表类型声明
    const firstLine = lines[0].toLowerCase()
    const validStarters = [
      'graph', 'flowchart', 'sequencediagram', 'classdiagram',
      'statediagram', 'erdiagram', 'journey', 'gantt', 'pie', 'gitgraph'
    ]
    
    return validStarters.some(starter => firstLine.startsWith(starter))
  } catch (error) {
    return false
  }
}
