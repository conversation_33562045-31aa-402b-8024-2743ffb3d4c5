/**
 * Mermaid自定义指令
 * 用于在DOM中渲染Mermaid图表
 */

import { createApp } from 'vue'
import MermaidRenderer from '../components/MermaidRenderer.vue'

// 存储已创建的Vue应用实例
const mermaidApps = new Map()

/**
 * 清理指定元素的Mermaid应用
 */
function cleanupMermaidApp(el) {
  const app = mermaidApps.get(el)
  if (app) {
    app.unmount()
    mermaidApps.delete(el)
  }
}

/**
 * 创建并挂载Mermaid组件
 */
function createMermaidComponent(el, code, isStreaming = false) {
  // 清理现有的应用
  cleanupMermaidApp(el)

  console.log('MermaidDirective: Creating component', { code: code.substring(0, 50) + '...', isStreaming })

  try {
    // 创建新的Vue应用
    const app = createApp(MermaidRenderer, {
      code,
      isStreaming
    })

    // 挂载到元素
    app.mount(el)

    // 存储应用实例
    mermaidApps.set(el, app)

    console.log('MermaidDirective: Component created successfully')
  } catch (error) {
    console.error('MermaidDirective: Failed to create component', error)
    el.innerHTML = '<div class="mermaid-error">组件创建失败</div>'
  }
}

/**
 * 处理包含Mermaid占位符的HTML内容
 */
function processMermaidPlaceholders(el) {
  // 查找所有Mermaid占位符
  const mermaidWrappers = el.querySelectorAll('.mermaid-wrapper')

  console.log('MermaidDirective: Found wrappers:', mermaidWrappers.length)

  mermaidWrappers.forEach((wrapper, index) => {
    const encodedCode = wrapper.getAttribute('data-mermaid-code')
    const isStreaming = wrapper.getAttribute('data-is-streaming') === 'true'

    console.log(`MermaidDirective: Processing wrapper ${index}`, { encodedCode: !!encodedCode, isStreaming })

    if (encodedCode) {
      try {
        const code = decodeURIComponent(encodedCode)
        console.log('MermaidDirective: Decoded code:', code)
        createMermaidComponent(wrapper, code, isStreaming)
      } catch (error) {
        console.warn('Failed to decode Mermaid code:', error)
        wrapper.innerHTML = '<div class="mermaid-error">图表渲染失败</div>'
      }
    }
  })
}

/**
 * Mermaid指令定义
 */
export const mermaidDirective = {
  name: 'mermaid',
  
  mounted(el, binding) {
    // 处理初始内容
    processMermaidPlaceholders(el)
  },
  
  updated(el, binding) {
    // 内容更新时重新处理
    setTimeout(() => {
      processMermaidPlaceholders(el)
    }, 0)
  },
  
  beforeUnmount(el) {
    // 清理所有子元素的Mermaid应用
    const mermaidWrappers = el.querySelectorAll('.mermaid-wrapper')
    mermaidWrappers.forEach(wrapper => {
      cleanupMermaidApp(wrapper)
    })
  }
}

/**
 * 安装指令的函数
 */
export function installMermaidDirective(app) {
  app.directive('mermaid', mermaidDirective)
}

export default mermaidDirective
