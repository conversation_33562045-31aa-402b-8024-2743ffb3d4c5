import Cookies from "js-cookie";
import { getSessionStorage, setSessionStorage, setLocalStorage} from "~/utils";

// import { useUserStore } from "~/store/user";

export const sessionKey = "user-info11";
export const TokenKey = "authorized-token11";

/** 获取`token` */
export function getToken(sessionKey) {
  // console.log('🍧-----sessionKey-----', sessionKey);
  // console.log('🎁-----Cookies.get(TokenKey)-----', Cookies.get(TokenKey));
  return Cookies.get(TokenKey)
    ? JSON.parse(Cookies.get(TokenKey))
    : getSessionStorage(sessionKey);
}

export function setToken(data = {}) {
  // console.log('🐳-----data-----', data);
  // console.log("setToken", data);
  const { accessToken, refresh_token, expiresIn } = data || {};
  const expires = expiresIn ? new Date().getTime() + expiresIn * 1000 : 0;
  const cookieString = JSON.stringify({ accessToken, refresh_token, expires });
  expires > 0
    ? Cookies.set(TokenKey, cookieString, {
        expires: expires / 86400000,
      })
    : Cookies.set(TokenKey, cookieString);
  setSessionStorage(sessionKey, data);
  setLocalStorage(sessionKey, data)
}

/** 删除`token`以及key值为`user-info`的session信息 */
export function removeToken(sessionKey) {
  Cookies.remove(TokenKey);
  sessionStorage.clear(sessionKey);
}

/** 格式化token（jwt格式） */
export const formatToken = (token) => {
  return token;
  return "Bearer " + token;
};
