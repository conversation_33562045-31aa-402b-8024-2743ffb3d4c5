<p align='center'>
vue3-template-js <b>vite + v3 +js</b><sup><em>(speed)</em></sup><br>
</p>

<br>

<p align='center'>
<a href="https://starter-web.netlify.app/">Live Demo</a>
</p>

<br>

<!-- <p align='center'>
<b>English</b> | <a href="">简体中文</a>
</p> -->

<br>

## Features

- ⚡️ [Vue 3](https://github.com/vuejs/core), [Vite](https://github.com/vitejs/vite), [pnpm](https://pnpm.io/)

- 📦 [Components auto importing](./src/components)

- 🍍 [State Management via Pinia](https://pinia.vuejs.org/)

- 🎨 [UnoCSS](https://github.com/antfu/unocss) - the instant on-demand atomic CSS engine

- 😃 [Use icons from any icon sets with classes](https://github.com/antfu/unocss/tree/main/packages/preset-icons)

- 📥 [APIs auto importing](https://github.com/antfu/unplugin-auto-import) - use Composition API and others directly

- 🦾 [Api](./src/api) - a simple wrapper for [axios]

- 🎨 [Element Plus](https://element-plus.org/) - a Vue 3.0 UI library

<br>

## Usage

### Development

Just run and visit http://localhost:9527

```bash
pnpm dev
```

### Build

To build the App, run

```bash
pnpm build
```

And you will see the generated file in `dist` that ready to be served.
