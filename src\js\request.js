import { Message } from 'element-ui';

function objToQueryParam(obj) {
  return Object.entries(obj)
    .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)
    .join('&');
}
export default {
  // get(url, param) {
  //   return fetch(`${url}?${objToQueryParam(param)}`)
  //     .then((response) => response.json());
  // },
  get(url, param) {
    console.log('🍪-----param-----', param);
    return fetch(`${url}?${objToQueryParam(param)}`).then((response) => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    }).then((data) => {
      // 在这里对返回的 JSON 数据进行自定义处理
      // 例如，添加额外的逻辑或转换数据格式
      console.log('Customized response data:', data);
      if (data.responseNo === 10003) {
        console.log('🦄-----this-----', this);
        Message({
          showClose: true,
          message: '你的登录授权信息已过期或已在其他设备上登录，请重新登录！',
          type: 'warning'
        });
        setTimeout(() => {
          const { origin, pathname } = window.location;
          window.open(origin + pathname, '_self');
          console.log('🐳-----origin+pathname-----', origin + pathname);
        }, 1000);
      }
      return data; // 返回处理后的数据
    });
  },
  getInNewTab(url, param) {
    const URL = `${url}?${objToQueryParam(param)}`;
    window.open(URL);
  },
  post(url, data) {
    return fetch(url, {
      method: 'POST',
      body: objToQueryParam(data),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    }).then((response) => response.json());
  },
  put(url, data) {
    return fetch(url, {
      method: 'PUT',
      body: objToQueryParam(data),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    }).then((response) => response.json());
  },
  delete(url, param) {
    return fetch(`${url}?${objToQueryParam(param)}`, {
      method: 'DELETE',
    }).then((response) => response.json());
  },
};
