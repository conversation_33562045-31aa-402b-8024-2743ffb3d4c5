// 打字机效果函数，处理 HTML 标签和换行符的渲染
const typeWriterEffect = (el, html, index, cb) => {
  if (index < html.length) {
    let char = html[index];

    if (char === '\n') {
      // 检测换行符，将其转换为 <br> 标签
      el.innerHTML += '<br/>';
      index++; // 处理完换行符后，移动到下一个字符
    } else if (html.slice(index, index + 4) === '&lt;') {
      // 找到 '&lt;'，处理为标签的开头
      let endIndex = html.indexOf('&gt;', index) + 4; // 找到标签结束 '&gt;'
      const tagContent = html.slice(index + 4, endIndex - 4); // 提取标签内容
      el.innerHTML += `<${tagContent}>`; // 转换为真正的 HTML 标签
      index = endIndex; // 跳过整个标签内容
    } else {
      // 正常输出字符
      el.innerHTML += char;
      index++;
    }

    // 递归调用直到文本结束
    setTimeout(() => {
      typeWriterEffect(el, html, index, cb);
    }, 50); // 控制打字速度
  }
  cb(); // 打字效果完成，执行回调函数
};

export const type = {
  mounted(el, binding) {
    const { value } = binding;
    const copyValue = value.copyValue;
    let applyEffect = binding.value?.applyEffect; // 是否应用打字机效果
    const cb = binding.value?.callback;
    // let char ='&lt;strong style="color: #2d91f8;">HHHHH</strong><br&gt;, 1 < 2 and 3 > 1';

    if (applyEffect) {
      el.innerHTML = ''; // 清空元素内容以便于逐字添加
      typeWriterEffect(el, copyValue, 0, cb); // 调用打字机效果函数
    } else {
      // el.innerHTML = copyValue.replace(/\n/g, '<br>'); // 如果不应用打字机效果，直接显示文本
      // console.log('🦄-----copyValue-----', copyValue);
      // el.innerHTML = copyValue.replace(/\n/g, '<br>'); // 如果不应用打字机效果，直接显示文本
      // el.innerHTML = `<h1>${copyValue.title}</h1><p>${copyValue.content.replace(/。/g, '。</p><p>')}</p>`; // 如果不应用打字机效果，直接显示文本
      // if (typeof copyValue === 'object' && copyValue !== null) {
      //   console.log('哈哈哈哈哈哈哈哈哈');
      // }
      el.innerHTML = copyValue.replace(/\n/g, '<br>'); // 如果不应用打字机效果，直接显示文本
      // console.log('🐳----- el.innerHTML-----',  el.innerHTML);
      // cb(); // 直接调用回调函数
    }
  },
};
