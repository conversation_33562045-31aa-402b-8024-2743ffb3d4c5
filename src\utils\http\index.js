import Axios from "axios";
import { apiServer } from "./domain.js";
import NProgress from "../progress";
import { setToken, getToken, formatToken } from "../auth.js";
import { ElMessage } from "element-plus";
import { useUserStore } from "~/store/user";
import { encrypt } from "~/utils/auth/sign";
import { removeToken } from "../../utils/auth.js";

const defaultConfig = {
  // baseURL: VITE_PROXY_DOMAIN_REAL,
  // 当前使用mock模拟请求，将baseURL制空
  baseURL: apiServer["apiServer"],
  // 请求超时时间
  timeout: 12000,
  // headers: {
  //   Accept: "application/json, text/plain, */*",
  //   "Content-Type": "application/json",
  //   "X-Requested-With": "XMLHttpRequest",
  // },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  // paramsSerializer: {
  //   serialize: stringify as unknown as CustomParamsSerializer,
  // },
};

class PureHttp {
  constructor() {
    PureHttp.httpInterceptorsResponse();

    PureHttp.httpInterceptorsRequest();
  }
  /** token过期后，暂存待执行的请求 */
  static requests = [];

  /** 防止重复刷新token */
  static isRefreshing = false;

  /** 初始化配置对象 */
  static initConfig = {};

  /** 保存当前Axios实例对象 */
  static axiosInstance = Axios.create(defaultConfig);

  // 是否返回全部数据
  static isNeedFullRes = false;

  // 是否显示loading
  static isShowLoading = false;

  // 是否需要token
  static isNeedToken = true;

  // 是否是上传
  static isUpload = false;

  // 根据角色切换token
  static tokenRoleName = "";

  // servername
  static serverName = "apiServer";

  // 接口是否异常
  static isApiError = false;
  // 业务异常code名单
  static errorCodes = [401, 11012];

  /** 重连原始请求 */
  static retryOriginalRequest(config) {
    return new Promise((resolve) => {
      PureHttp.requests.push((token) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  // 请求拦截器
  static httpInterceptorsRequest() {
    PureHttp.axiosInstance.interceptors.request.use(
      async (config) => {
        //  console.log('🌈-----config-----', config);
        const {
          isNeedToken = true,
          isNeedFullRes = false,
          isShowLoading = false,
          isUpload = false,
          isNeedEncrypt = false,
          isFormDataEncrypt = false,
          isFormUrlEncoded = false, // 新增配置项，判断是否为 x-www-form-urlencoded
          tokenRoleName = "",
          serverName = "",
        } = config;
        PureHttp.isNeedToken = isNeedToken;
        PureHttp.isShowLoading = isShowLoading;
        PureHttp.isNeedFullRes = isNeedFullRes;
        // 开启进度条动画
        if (PureHttp.isShowLoading && !PureHttp.isApiError) {
          NProgress.start();
        }

        // 参数处理
        if (isNeedEncrypt) {
          const { data, method, params } = config;
          let postData = {};
          if (isFormDataEncrypt) {
            data.forEach((value, key) => {
              console.log("🍧-----value-----", value);
              AuthorizationData[key] = value;
            });
          } else {
            postData = data;
            // console.log('🎉-----postData-----', postData);
          }
          const { nonce, timestamp, sign } = encrypt(
            method === "get" ? params : postData,
            serverName
          );
          // console.log('🍧-----postData-----', postData);
          config.headers.timestamp = timestamp;
          config.headers.nonce = nonce;
          config.headers.sign = sign;
          if (method === "get") {
            config.params = params;
          } else {
            config.data = data;
          }
        }

        if (serverName) {
          config.baseURL = apiServer[serverName] || apiServer["apiServer"];
        }
        // 处理 x-www-form-urlencoded 方式的请求
        if (isFormUrlEncoded) {
          config.headers["Content-Type"] = "application/x-www-form-urlencoded";
          config.data = new URLSearchParams(config.data).toString();
        }

        // 优先判断post/get等方法是否传入回掉，否则执行初始化设置等回掉
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }

        return PureHttp.isNeedToken
          ? new Promise((resolve) => {
              const token = tokenRoleName
                ? getToken(tokenRoleName)
                : getToken();
              // console.log('🦄-----token-----',getToken() );
              const { accessToken, expires_in } = token || {};
              if (config.isUpload) {
                config.headers["x-auth-token"] = getLocalStorage("uploadToken");
              }
              if (accessToken) {
                const now = new Date().getTime();
                const expired = parseInt("" + expires_in) - now <= 0;
                // console.log(expired,"expired");
                if (expired) {
                  console.log("token过期");
                  resolve(config);
                } else {
                  // console.log('token未过期', access_token)
                  if (!config.isUpload) {
                    config.headers.Authorization = accessToken;
                    // if (
                    //   config.url == "/teacher/roster/listVerification" ||
                    //   config.url == "/teacher/roster/importList"
                    // ) {
                    //   console.log(config.url, "00000000000000");
                    //   config.headers.dslToken =
                    //     "Bearer " + localStorage.getItem("dslToken");
                    // }
                  }

                  resolve(config);
                }
              } else {
                console.log("token不存在");
                resolve(config);
              }
            })
          : config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  // 响应拦截器
  static httpInterceptorsResponse() {
    PureHttp.axiosInstance.interceptors.response.use(
      (response) => {
        // 关闭进度条动画
        if (PureHttp.isShowLoading && !PureHttp.isApiError) {
          NProgress.done();
        }

        const { code } = response.data;
        // 业务异常code名单
        if (PureHttp.errorCodes.includes(code)) {
          PureHttp.isApiError = true;
          // 业务异常逻辑
          // ElMessage({
          //   message: '请求异常，请稍后再试',
          //   type: 'error',
          // });
          console.log("请求异常，请稍后再试", code, "过期");
          removeToken("authorized-token11");
          location.reload(true);
          return response;
          // return Promise.reject(response)
        }

        const tempResponse = ({} = PureHttp.isNeedFullRes
          ? response
          : response.data);

        // 优先判断post/get等方法是否传入回掉，否则执行初始化设置等回掉
        if (typeof response.config.afterResponseCallback === "function") {
          response.config.afterResponseCallback(response);
          return tempResponse;
        }
        if (PureHttp.initConfig.afterResponseCallback) {
          PureHttp.initConfig.afterResponseCallback(response);
          return tempResponse;
        }

        return tempResponse;
      },
      (error) => {
        console.log("error", error);
        // 关闭进度条动画
        if (!PureHttp.isApiError) {
          NProgress.done();
        }
        // 优先判断post/get等方法是否传入回掉，否则执行初始化设置等回掉
        if (typeof error.config.afterResponseCallback === "function") {
          error.config.afterResponseCallback(error);
          return Promise.resolve({
            msg: "请求异常，请稍后再试",
            ...error,
          });
        }
        if (PureHttp.initConfig.afterResponseCallback) {
          PureHttp.initConfig.afterResponseCallback(error);
          return Promise.resolve({
            msg: "请求异常，请稍后再试",
            ...error,
          });
        }
        return Promise.resolve({
          msg: "请求异常，请稍后再试",
          ...error,
        });
      }
    );
  }

  /** 通用请求工具函数 */
  request(method, url, param = {}, axiosConfig = {}) {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig,
    };
    // 单独处理自定义请求/响应回掉
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response) => {
          resolve(response);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
}

export const http = new PureHttp();
