import { createApp } from "vue";
import { router } from "./router";
import routes from "virtual:generated-pages";

import App from "./App.vue";

import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { MotionPlugin } from "@vueuse/motion";
import ElementPlusX from "vue-element-plus-x";

import "element-plus/dist/index.css";
import "./styles/main.css";
// import '@unocss/reset/tailwind.css'
import "uno.css";
import { type as vType } from "~/directives/type";

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
const app = createApp(App);

app.use(MotionPlugin);
app.use(pinia);
app.use(router);
app.use(ElementPlusX);
app.directive("type", vType);

app.mount("#app");
