import { http } from "../utils/http";
/** 附件预览 */
// `${'https://api.ebag-test.readboy.com/wps'}${'/v1/student/office/url'}`,
export const officeUrl = (params) => {
  return http.request(
      "get",
      "/v1/student/office/url",
      { params },
      {
        serverName: 'csServer',
        isNeedFullRes: false, // 是否需要返回完整的响应对象
        isNeedToken: false, // 是否需要token
        isNeedEncrypt: false
    }
  );
};
//查询消息列表
export const findAll = (params) => {
  return http.request(
    "get",
    "/api/question/findAll",
    { params },
    {
      isNeedToken: true, // 是否需要token
      isNeedEncrypt: true,
    }
  );
};

// 提问-提交
export const askAll = (data) => {
  return http.request(
    "post",
    "/api/question/ask",
    { data },
    {
      isNeedToken: true, // 是否需要token
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedEncrypt: true,
    }
  );
};

//凭证试获取token
export const clientToken = (data) => {
  return http.request(
    "post",
    "/oauth2/client_token",
    { data },
    {
      isNeedToken: false,
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isFormUrlEncoded:true, //是否需要 x-www-form-urlencoded
      // isNeedEncrypt: true,

    }
  );
};

//登录
export const clientlogin = (data) => {
  return http.request(
    "post",
    "/api/user/login",
    { data },
    {
      isNeedToken: false,
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isFormUrlEncoded:false, //是否需要 x-www-form-urlencoded
      // isNeedEncrypt: true,

    }
  );
};
//评价（点赞-踩）
export const questionEvaluate = (data) => {
  return http.request(
    "post",
    "/api/question/evaluate",
    { data },
    {
      isNeedToken: true,
      isNeedFullRes: true, // 是否需要返回完整的响应对象
      isNeedEncrypt: true,
    }
  );
};
//查看ppt-word文件
export const findFile = (params) => {
  return http.request(
    "get",
    "/api/question/findFile",
    { params },
    {
      isNeedToken: true,
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedEncrypt: true,
    }
  );
};
/** 根据id查询异步任务 */
export const asyncTask = (params) => {
  return http.request(
    "get",
    "/common/findById",
    { params },
    {
      isNeedToken: true, // 是否需要token
      isNeedFullRes: false, // 是否需要返回完整的响应对象
      isNeedEncrypt: true,
    }
  );
};
/** 根据问题Id查询详情 */
export const questionFindById = (params) => {
  return http.request(
    "get",
    "/api/question/findById",
    { params },
    {
      isNeedToken: true, // 是否需要token
      isNeedEncrypt: true,
    }
  );
};
/** 查看ppt-word文件v2 */
export const questfindFileV2 = (params) => {
  return http.request(
    "get",
    "/api/question/findFileV2",
    { params },
    {
      isNeedToken: true, // 是否需要token
      isNeedEncrypt: true,
    }
  );
};
/** 查看json是否完成转换 */
export const checkJson = (params) => {
  return http.request(
    "get",
    "/api/question/checkJson",
    { params },
    {
      isNeedToken: true, // 是否需要token
      isNeedEncrypt: true,
    }
  );
};