/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DataPreviewer: typeof import('./src/components/DataPreviewer.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Popup: typeof import('./src/components/Popup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheFooter: typeof import('./src/components/TheFooter.vue')['default']
    TheHeader: typeof import('./src/components/TheHeader.vue')['default']
  }
}
