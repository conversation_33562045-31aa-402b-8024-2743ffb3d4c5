# Mermaid 图表测试

## 1. 流程图测试

```mermaid
graph TD
    A[开始] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[跳转登录页]
    C --> E[用户操作]
    D --> F[用户登录]
    F --> C
    E --> G[结束]
```

## 2. 序列图测试

```mermaid
sequenceDiagram
    participant 用户
    participant 前端
    participant 后端
    participant 数据库
    
    用户->>前端: 发送请求
    前端->>后端: API调用
    后端->>数据库: 查询数据
    数据库-->>后端: 返回结果
    后端-->>前端: 响应数据
    前端-->>用户: 显示结果
```

## 3. 类图测试

```mermaid
classDiagram
    class User {
        +String name
        +String email
        +login()
        +logout()
    }
    
    class Admin {
        +String role
        +manageUsers()
    }
    
    User <|-- Admin
```

## 4. 状态图测试

```mermaid
stateDiagram-v2
    [*] --> 待处理
    待处理 --> 处理中: 开始处理
    处理中 --> 已完成: 处理完成
    处理中 --> 失败: 处理失败
    失败 --> 待处理: 重新处理
    已完成 --> [*]
```

## 5. 甘特图测试

```mermaid
gantt
    title 项目开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求收集           :done,    des1, 2024-01-01,2024-01-05
    需求分析           :done,    des2, after des1, 5d
    section 设计阶段
    UI设计            :active,  des3, 2024-01-10, 3d
    架构设计           :         des4, after des3, 5d
    section 开发阶段
    前端开发           :         des5, 2024-01-20, 10d
    后端开发           :         des6, 2024-01-20, 12d
```

## 6. 饼图测试

```mermaid
pie title 用户分布
    "新用户" : 42.96
    "活跃用户" : 50.05
    "流失用户" : 7.01
```
